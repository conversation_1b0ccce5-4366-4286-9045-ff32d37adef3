{"name": "kreait/firebase-php", "description": "Firebase Admin SDK", "keywords": ["firebase", "google", "sdk", "api", "database"], "type": "library", "homepage": "https://github.com/kreait/firebase-php", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://github.com/jeromegamez"}], "support": {"docs": "https://firebase-php.readthedocs.io", "issues": "https://github.com/kreait/firebase-php/issues", "source": "https://github.com/kreait/firebase-php"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jeromegamez"}], "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "ext-ctype": "*", "ext-filter": "*", "ext-json": "*", "ext-mbstring": "*", "beste/clock": "^3.0", "beste/in-memory-cache": "^1.3.1", "beste/json": "^1.5.1", "fig/http-message-util": "^1.1.5", "firebase/php-jwt": "^6.10.2", "google/auth": "^v1.45", "google/cloud-storage": "^1.45", "guzzlehttp/guzzle": "^7.9.2", "guzzlehttp/promises": "^2.0.4", "guzzlehttp/psr7": "^2.7", "kreait/firebase-tokens": "^5.2", "lcobucci/jwt": "^4.3|^5.3", "mtdowling/jmespath.php": "^2.8.0", "psr/cache": "^1.0.1|^2.0|^3.0", "psr/clock": "^1.0", "psr/http-client": "^1.0.3", "psr/http-factory": "^1.1", "psr/http-message": "^1.1 || ^2.0", "psr/log": "^1.1|^2.0|^3.0.2"}, "require-dev": {"php-cs-fixer/shim": "^3.68.5", "google/cloud-firestore": "^1.47.2", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.0.4", "phpstan/phpstan-deprecation-rules": "^2.0.1", "phpstan/phpstan-phpunit": "^2.0.3", "phpunit/phpunit": "^10.5.39", "rector/rector": "^2.0.3", "shipmonk/composer-dependency-analyser": "^1.8.1", "symfony/var-dumper": "^6.4.15 || ^7.2.0", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"google/cloud-firestore": "^1.0 to use the Firestore component"}, "autoload": {"psr-4": {"Kreait\\Firebase\\": "src/Firebase"}}, "autoload-dev": {"psr-4": {"Kreait\\Firebase\\Tests\\": "tests"}}, "config": {"sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true}}, "scripts": {"analyse": ["XDEBUG_MODE=off vendor/bin/phpstan"], "analyze": "@analyse", "cs": ["PHP_CS_FIXER_IGNORE_ENV=true vendor/bin/php-cs-fixer fix --diff --verbose"], "docs": ["cd docs && make html"], "pre-push": ["@rector-fix", "@test", "@test-bc"], "rector": ["vendor/bin/rector --dry-run"], "rector-fix": ["vendor/bin/rector", "@cs"], "reset-project": ["tests/bin/reset-project"], "test": ["Composer\\Config::disableProcessTimeout", "@analyze", "@test-dependencies", "vendor/bin/phpunit --stop-on-error --stop-on-failure"], "test-bc": ["docker run -it --rm -v `pwd`:/app nyholm/roave-bc-check"], "test-dependencies": ["vendor/bin/composer-dependency-analyser"], "test-coverage": ["Composer\\Config::disableProcessTimeout", "XDEBUG_MODE=coverage vendor/bin/phpunit --coverage-html=.build/coverage"], "test-units": ["vendor/bin/phpunit --testsuite=unit"], "test-integration": ["vendor/bin/phpunit --testsuite=integration"]}, "extra": {"branch-alias": {"dev-7.x": "7.x-dev"}}}