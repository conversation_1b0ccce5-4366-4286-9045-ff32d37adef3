{"name": "php-webdriver/webdriver", "description": "A PHP client for Selenium WebDriver. Previously facebook/webdriver.", "license": "MIT", "type": "library", "keywords": ["webdriver", "selenium", "php", "geckodriver", "chromedriver"], "homepage": "https://github.com/php-webdriver/php-webdriver", "require": {"php": "^7.3 || ^8.0", "ext-curl": "*", "ext-json": "*", "ext-zip": "*", "symfony/polyfill-mbstring": "^1.12", "symfony/process": "^5.0 || ^6.0 || ^7.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.20.0", "ondram/ci-detector": "^4.0", "php-coveralls/php-coveralls": "^2.4", "php-mock/php-mock-phpunit": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.5", "symfony/var-dumper": "^5.0 || ^6.0 || ^7.0"}, "replace": {"facebook/webdriver": "*"}, "suggest": {"ext-SimpleXML": "For Firefox profile creation"}, "minimum-stability": "dev", "autoload": {"psr-4": {"Facebook\\WebDriver\\": "lib/"}, "files": ["lib/Exception/TimeoutException.php"]}, "autoload-dev": {"psr-4": {"Facebook\\WebDriver\\": ["tests/unit", "tests/functional"]}, "classmap": ["tests/functional/"]}, "config": {"allow-plugins": {"ergebnis/composer-normalize": true}, "sort-packages": true}, "scripts": {"post-install-cmd": ["@composer install --working-dir=tools/php-cs-fixer --no-progress --no-interaction", "@composer install --working-dir=tools/phpstan --no-progress --no-interaction"], "post-update-cmd": ["@composer update --working-dir=tools/php-cs-fixer --no-progress --no-interaction", "@composer update --working-dir=tools/phpstan --no-progress --no-interaction"], "all": ["@lint", "@analyze", "@test"], "analyze": ["@php tools/phpstan/vendor/bin/phpstan analyze -c phpstan.neon --ansi", "@php tools/php-cs-fixer/vendor/bin/php-cs-fixer fix --diff --dry-run -vvv --ansi", "@php vendor/bin/phpcs --standard=PSR2 --ignore=*.js ./lib/ ./tests/"], "fix": ["@composer normalize", "@php tools/php-cs-fixer/vendor/bin/php-cs-fixer fix --diff -vvv || exit 0", "@php vendor/bin/phpcbf --standard=PSR2 --ignore=*.js ./lib/ ./tests/"], "lint": ["@php vendor/bin/parallel-lint -j 10 ./lib ./tests example.php", "@composer validate", "@composer normalize --dry-run"], "test": ["@php vendor/bin/phpunit --colors=always"]}}