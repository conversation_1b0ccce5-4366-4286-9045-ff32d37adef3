
div.phpdebugbar-widgets-templates div.phpdebugbar-widgets-status {
  font-family: var(--debugbar-font-mono);
  padding: 6px 6px;
  border-bottom: 1px solid var(--debugbar-border);
  font-weight: bold;
  color: var(--debugbar-text);
  background-color: var(--debugbar-background-alt);
}

div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-render-time,
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-memory,
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-param-count,
div.phpdebugbar-widgets-templates a.phpdebugbar-widgets-editor-link,
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-type {
  float: right;
  margin-left: 8px;
  color: var(--debugbar-text);
}
div.phpdebugbar-widgets-templates div.phpdebugbar-widgets-status span.phpdebugbar-widgets-render-time,
div.phpdebugbar-widgets-templates div.phpdebugbar-widgets-status span.phpdebugbar-widgets-memory,
div.phpdebugbar-widgets-templates div.phpdebugbar-widgets-status span.phpdebugbar-widgets-param-count,
div.phpdebugbar-widgets-templates div.phpdebugbar-widgets-status a.phpdebugbar-widgets-editor-link,
div.phpdebugbar-widgets-templates div.phpdebugbar-widgets-status span.phpdebugbar-widgets-type {
  color: var(--debugbar-text);
}
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-render-time:before,
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-memory:before,
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-param-count:before,
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-type:before,
div.phpdebugbar-widgets-templates a.phpdebugbar-widgets-editor-link:before,
div.phpdebugbar-widgets-templates a.phpdebugbar-widgets-editor-link:before
{
  font-family: PhpDebugbarFontAwesome;
  margin-right: 4px;
  font-size: 12px;
}
div.phpdebugbar-widgets-templates a.phpdebugbar-widgets-editor-link:hover
{
    color: var(--debugbar-hover);
}
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-render-time:before {
  content: "\f017";
}
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-memory:before {
  content: "\f085";
}
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-param-count:before {
  content: "\f0ce";
}
div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-type:before {
  content: "\f121";
}
div.phpdebugbar-widgets-templates a.phpdebugbar-widgets-editor-link:before {
  content: "\f08e";
  margin-left: 4px;
}
div.phpdebugbar-widgets-templates table.phpdebugbar-widgets-params {
  display: none;
  width: 70%;
  margin: 10px;
  border: 1px solid var(--debugbar-border);
  font-family: var(--debugbar-font-mono);
  border-collapse: collapse;
}
div.phpdebugbar-widgets-templates table.phpdebugbar-widgets-params td {
  border: 1px solid var(--debugbar-border);
  padding: 0 5px;
}
div.phpdebugbar-widgets-templates table.phpdebugbar-widgets-params .phpdebugbar-widgets-name {
  width: 20%;
  font-weight: bold;
}
