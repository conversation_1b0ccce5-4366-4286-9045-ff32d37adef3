<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    5042200 => 'Polo Paz',
    5042201 => 'Polo Paz',
    5042202 => 'Tegucigalpa',
    5042203 => 'Polo Paz',
    5042204 => 'Francisco Morazan',
    5042209 => 'Res. Centro América, Tegucigalpa',
    5042211 => 'El Picacho',
    5042212 => 'Rdsi Tegucigalpa (Pri3)',
    5042213 => 'Telef. Inalámbrica Tegucig.',
    5042214 => 'Francisco Morazan',
    5042215 => 'Francisco Morazan',
    5042216 => 'Rdsi Tegucigalpa (Pri3)',
    5042218 => '<PERSON>',
    5042219 => '<PERSON>',
    5042220 => 'Principal',
    5042221 => 'Almendros',
    5042222 => 'Principal',
    5042223 => 'Polo Paz',
    5042224 => 'Cerro Grande',
    5042225 => 'La Granja',
    5042226 => 'Loarque',
    5042227 => 'Res. Centro América, Tegucigalpa',
    5042228 => 'Kennedy, Tegucigalpa',
    5042229 => 'El Ocotal',
    5042230 => 'Kennedy, Tegucigalpa',
    5042231 => 'Miraflores',
    5042232 => 'Miraflores',
    5042233 => 'Toncontín',
    5042234 => 'Toncontín',
    5042235 => 'Miraflores',
    5042236 => 'Almendros',
    5042237 => 'Principal',
    5042238 => 'Principal',
    5042239 => 'Miraflores',
    5042240 => 'Kennedy, Tegucigalpa',
    5042242 => 'Francisco Morazan',
    5042244 => 'Tegucigalpa',
    5042245 => 'La Vega, Tegucigalpa',
    5042246 => 'La Vega, Tegucigalpa',
    5042255 => 'El Hato',
    5042257 => 'Prados Universitarios',
    5042281 => 'Francisco Morazan',
    5042283 => 'Francisco Morazan',
    5042290 => 'Toncontin',
    5042291 => 'Toncontin',
    5042405 => 'Atlantida',
    5042407 => 'Roatán, Bay Islands',
    5042423 => 'La Ceiba',
    5042424 => 'Sabá',
    5042425 => 'Utila, Bay Islands',
    5042429 => 'San Alejo/Mesapa',
    5042431 => 'San Francisco, Atlántida',
    5042433 => 'Arenal',
    5042434 => 'Trujillo',
    5042435 => 'Oakridge',
    5042436 => 'La Masica',
    5042438 => 'Bonito Oriental',
    5042440 => 'La Ceiba',
    5042442 => 'La Ceiba',
    5042443 => 'La Ceiba',
    5042444 => 'Tocoa, Colón',
    5042445 => 'Coxin Hole, Roatán',
    5042446 => 'Olanchito',
    5042448 => 'Tela',
    5042451 => 'Sonaguera',
    5042452 => 'Coyoles Central',
    5042453 => 'Guanaja',
    5042455 => 'French Harbour',
    5042458 => 'Atlantida',
    5042459 => 'Atlantida',
    5042502 => 'Cortes',
    5042503 => 'Cortes',
    5042505 => 'Cortes',
    5042511 => 'Cortes',
    5042512 => 'San Pedro Sula, Cortés',
    5042513 => 'Cortes',
    5042515 => 'Cortes',
    5042516 => 'San Pedro Sula, Cortés',
    5042540 => 'San Pedro Sula, Cortés',
    5042543 => 'Inalámbrica Sps',
    5042544 => 'Rdsi San Pedro Sula',
    5042545 => 'San Pedro Sula, Cortés',
    5042550 => 'San Pedro Sula, Cortés',
    5042551 => 'Monte Prieto',
    5042552 => 'San Pedro Sula, Cortés',
    5042553 => 'San Pedro Sula, Cortés',
    5042554 => 'Monte Prieto',
    5042555 => 'Rivera Hernandez, San Pedro Sula',
    5042556 => 'La Puerta',
    5042557 => 'San Pedro Sula, Cortés',
    5042558 => 'San Pedro Sula, Cortés',
    5042559 => 'Col. Satélite',
    5042564 => 'San Pedro Sula, Cortés',
    5042565 => 'Chamelecón',
    5042566 => 'Jardines Del Valle',
    5042569 => 'Cortes',
    5042570 => 'Cortes',
    5042574 => 'Búfalo',
    504261 => 'Choloma, Cortés',
    5042637 => 'Santa Barbra',
    5042640 => 'C. Comunitarios',
    5042641 => 'C. Comunitarios',
    5042642 => 'C. Comunitarios',
    5042643 => 'Santa Bárbara',
    5042647 => 'Progreso',
    5042648 => 'Progreso/Santa Cruz',
    5042650 => 'San Manuel/Rio Lindo',
    5042651 => 'Cucuyagua/Copán',
    5042652 => 'Agua Caliente',
    5042653 => 'Nueva Ocotepeque',
    5042654 => 'Santa Cruz',
    5042655 => 'Lepaera/Corquín',
    5042656 => 'Gracias/S.R.Copán',
    5042657 => 'El Naranjo Sta Bárbara',
    5042658 => 'Macueliso Omoa/Trascerros',
    5042659 => 'El Mochito/Quimistán',
    5042670 => 'Villa Nueva',
    5042671 => 'Yoro',
    5042672 => 'Cofradía',
    5042673 => 'Potrerillos',
    5042674 => 'Sulaco/Los Orcones',
    5042675 => 'Villa Nueva',
    5042678 => 'Potrerillos',
    504268 => 'La Lima',
    5042690 => 'El Negrito',
    5042691 => 'Morazán',
    504270 => 'Olancho',
    5042764 => 'Amarat/Marcala',
    5042766 => 'Valle De Ángeles',
    5042767 => 'Ojojona',
    5042768 => 'Sabana Grande',
    5042769 => 'Guaimaca',
    5042770 => 'Comayagua',
    5042772 => 'Comayagua',
    5042773 => 'Siguatepeque',
    5042774 => 'La Paz',
    5042775 => 'Talanga',
    5042776 => 'Zamorano',
    5042777 => 'Proyecto Ala',
    5042778 => 'Centros Comunitarios',
    5042779 => 'Santa Lucía',
    5042780 => 'Choluteca',
    5042783 => 'La Esperanza',
    5042784 => 'La Libertad',
    504287 => 'Choluteca',
    5042880 => 'Choluteca',
    5042881 => 'San Lorenzo',
    5042882 => 'Choluteca',
    5042883 => 'Danli',
    5042885 => 'Juticalpa',
    5042887 => 'Proyecto Ala',
    5042888 => 'S. Marcos/Proy. Ala',
    5042889 => 'Campamento',
    5042891 => 'S. Franc. De La Paz',
    5042892 => 'Yuscarán',
    5042893 => 'El Paraíso',
    5042894 => 'Amatillo/Goascorán',
    5042895 => 'Nacaome/Amapala',
    5042897 => 'San Fco. De Becerra',
    5042898 => 'Domsat',
    5042899 => 'Catacamas',
];
