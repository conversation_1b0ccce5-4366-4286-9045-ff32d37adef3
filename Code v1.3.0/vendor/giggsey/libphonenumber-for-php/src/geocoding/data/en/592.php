<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * <PERSON>ull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    592216 => 'Diamond/Grove',
    592217 => 'Mocha',
    592218 => 'Georgetown (S/R/Veldt)',
    592219 => 'Georgetown,Sophia',
    592220 => 'B/V Central',
    592221 => 'Mahaicony',
    592222 => 'B/V West',
    592223 => 'Georgetown',
    592225 => 'Georgetown',
    592226 => 'Georgetown',
    592227 => 'Georgetown',
    592228 => 'Mahaica/Belmont',
    592229 => 'Enterprise/Cove & John',
    592231 => 'Georgetown',
    592232 => 'Novar/Catherine/Belladrum/Bush Lot',
    592233 => 'Agricola/Houston/Eccles/Nandy Park',
    592234 => 'B/V Central',
    592253 => 'La Grange/Goed Fortuin',
    592254 => 'New Road/Best',
    592255 => 'Paradise/Golden Grove/Haslington',
    592256 => 'Victoria/Hope West',
    592257 => 'Cane Grove/Strangroen',
    592258 => 'Planters Hall/Mortice',
    592259 => 'Clonbrook/Unity',
    592260 => 'Tuschen/Parika',
    592261 => 'Timehri/Long Creek/Soesdyke',
    592262 => 'Parika',
    592264 => 'Vreed-en-Hoop',
    592265 => 'Diamond',
    592266 => 'New Hope/Friendship/Grove/Land of Canaan',
    592267 => 'Wales',
    592268 => 'Leonora',
    592269 => 'Windsor Forest',
    592270 => 'Melanie/Non Pariel/Enmore',
    592271 => 'Canal No. 1/Canal No. 2',
    592272 => 'B/V West',
    592274 => 'Vigilance',
    592275 => 'Met-en-Meer-Zorg',
    592276 => 'Anna Catherina/ Cornelia Ida/Hague/Fellowship',
    592277 => 'Zeeburg/Uitvlugt',
    592279 => 'Good Hope/Stanleytown',
    592322 => 'Kilcoy/Hampshire/Nigg',
    592325 => 'Mibikuri/No: 34/Joppa/Brighton',
    592326 => 'Adelphi/Fryish/No. 40',
    592327 => 'Blairmont/Cumberland',
    592328 => 'Cottage/Tempe/Onverwagt/Bath/Waterloo',
    592329 => 'Willemstad/Fort Wellington/Ithaca',
    592330 => 'Rosignol/Shieldstown',
    592331 => 'Adventure/Joanna',
    592332 => 'Sheet Anchor/Susannah',
    592333 => 'New Amsterdam',
    592334 => 'New Amsterdam',
    592335 => 'Crabwood Creek/No: 76/Corentyne',
    592336 => 'Edinburg/Port Mourant',
    592337 => 'Whim/Bloomfield/Liverpool/Rose Hall',
    592338 => 'Benab/No. 65 Village/Massiah',
    592339 => 'No: 52/Skeldon',
    592440 => 'Kwakwani',
    592441 => 'Ituni',
    592442 => 'Christianburg/Amelia’s Ward',
    592444 => 'Linden/Canvas City/Wisroc',
    592455 => 'Bartica',
    592456 => 'Mahdia',
    592772 => 'Lethem',
    592773 => 'Aishalton',
    592775 => 'Matthews Ridge',
    592777 => 'Mabaruma/Port Kaituma',
];
