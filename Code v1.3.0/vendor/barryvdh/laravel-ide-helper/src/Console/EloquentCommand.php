<?php

/**
 * <PERSON><PERSON> IDE Helper Generator - Eloquent Model Mixin
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Charles <PERSON> / Fruitcake Studio (http://www.fruitcakestudio.nl)
 * @license   http://www.opensource.org/licenses/mit-license.php MIT
 * @link      https://github.com/barryvdh/laravel-ide-helper
 */

namespace Barryvdh\LaravelIdeHelper\Console;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;

/**
 * A command to add \Eloquent mixin to Eloquent\Model
 *
 * <AUTHOR> <<EMAIL>>
 */
class EloquentCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'ide-helper:eloquent';

    /**
     * @var Filesystem $files
     */
    protected $files;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add \Eloquent helper to \Eloquent\Model';

    /**
     * @param Filesystem $files
     */
    public function __construct(Filesystem $files)
    {
        parent::__construct();
        $this->files = $files;
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Eloquent::writeEloquentModelHelper($this, $this->files);
    }
}
