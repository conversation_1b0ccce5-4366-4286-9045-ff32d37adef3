<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/consumer.proto

namespace GPBMetadata\Google\Api;

class Consumer
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
google/api/consumer.proto
google.api"=
ProjectProperties(

properties (2.google.api.Property"�
Property
name (	/
type (2!.google.api.Property.PropertyType
description (	"L
PropertyType
UNSPECIFIED 	
INT64
BOOL

STRING

DOUBLEBh
com.google.apiB
ConsumerProtoPZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfigbproto3'
        , true);

        static::$is_initialized = true;
    }
}

