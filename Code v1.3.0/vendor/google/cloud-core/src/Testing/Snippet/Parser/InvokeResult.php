<?php
/**
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

namespace Google\Cloud\Core\Testing\Snippet\Parser;

/**
 * Class InvokeResult
 */
class InvokeResult
{
    private $returnVal;
    private $output;

    /**
     * InvokeResult constructor.
     *
     * @param mixed $returnVal
     * @param mixed $output
     */
    public function __construct($returnVal, $output)
    {
        $this->returnVal = $returnVal;
        $this->output = $output;
    }

    /**
     * @return mixed
     */
    public function returnVal()
    {
        return $this->returnVal;
    }

    /**
     * @return mixed
     */
    public function output()
    {
        return $this->output;
    }
}
