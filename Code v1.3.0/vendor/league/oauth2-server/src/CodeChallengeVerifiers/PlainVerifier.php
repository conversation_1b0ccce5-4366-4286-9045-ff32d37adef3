<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) <PERSON><PERSON><PERSON><PERSON>
 * @license     http://mit-license.org/
 *
 * @link        https://github.com/thephpleague/oauth2-server
 */

namespace League\OAuth2\Server\CodeChallengeVerifiers;

class PlainVerifier implements CodeChallengeVerifierInterface
{
    /**
     * Return code challenge method.
     *
     * @return string
     */
    public function getMethod()
    {
        return 'plain';
    }

    /**
     * Verify the code challenge.
     *
     * @param string $codeVerifier
     * @param string $codeChallenge
     *
     * @return bool
     */
    public function verifyCodeChallenge($codeVerifier, $codeChallenge)
    {
        return \hash_equals($codeVerifier, $codeChallenge);
    }
}
