/**
 * @license <NAME_EMAIL>@ (@product.date@)
 * @module highcharts/modules/annotations
 * @requires highcharts
 *
 * Annotations module
 *
 * (c) 2009-2024 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */
'use strict';
import Highcharts from '../../Core/Globals.js';
import Annotation from '../../Extensions/Annotations/Annotation.js';
import NavigationBindings from '../../Extensions/Annotations/NavigationBindings.js';
const G = Highcharts;
G.Annotation = G.Annotation || Annotation;
G.NavigationBindings = G.NavigationBindings || NavigationBindings;
G.Annotation.compose(G.<PERSON>, G.NavigationBindings, G.<PERSON>er, G.SVGRenderer);
export default Highcharts;
