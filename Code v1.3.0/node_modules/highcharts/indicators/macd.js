!/**
 * Highstock JS v11.4.8 (2024-08-29)
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2024 <PERSON>
 *
 * License: www.highcharts.com/license
 */function(s){"object"==typeof module&&module.exports?(s.default=s,module.exports=s):"function"==typeof define&&define.amd?define("highcharts/indicators/macd",["highcharts","highcharts/modules/stock"],function(o){return s(o),s.Highcharts=o,s}):s("undefined"!=typeof Highcharts?Highcharts:void 0)}(function(s){"use strict";var o=s?s._modules:{};function t(o,t,e,i){o.hasOwnProperty(t)||(o[t]=i.apply(null,e),"function"==typeof CustomEvent&&s.win.dispatchEvent(new CustomEvent("HighchartsModuleLoaded",{detail:{path:t,module:o[t]}})))}t(o,"Stock/Indicators/MACD/MACDIndicator.js",[o["Core/Globals.js"],o["Core/Series/SeriesRegistry.js"],o["Core/Utilities.js"]],function(s,o,t){let{noop:e}=s,{column:i,sma:n}=o.seriesTypes,{extend:l,correctFloat:r,defined:a,merge:p}=t;class h extends n{init(){o.seriesTypes.sma.prototype.init.apply(this,arguments);let s=this.color;this.options&&(a(this.colorIndex)&&(this.options.signalLine&&this.options.signalLine.styles&&!this.options.signalLine.styles.lineColor&&(this.options.colorIndex=this.colorIndex+1,this.getCyclic("color",void 0,this.chart.options.colors),this.options.signalLine.styles.lineColor=this.color),this.options.macdLine&&this.options.macdLine.styles&&!this.options.macdLine.styles.lineColor&&(this.options.colorIndex=this.colorIndex+1,this.getCyclic("color",void 0,this.chart.options.colors),this.options.macdLine.styles.lineColor=this.color)),this.macdZones={zones:this.options.macdLine.zones,startIndex:0},this.signalZones={zones:this.macdZones.zones.concat(this.options.signalLine.zones),startIndex:this.macdZones.zones.length}),this.color=s}toYData(s){return[s.y,s.signal,s.MACD]}translate(){let o=this,t=["plotSignal","plotMACD"];s.seriesTypes.column.prototype.translate.apply(o),o.points.forEach(function(s){[s.signal,s.MACD].forEach(function(e,i){null!==e&&(s[t[i]]=o.yAxis.toPixels(e,!0))})})}destroy(){this.graph=null,this.graphmacd=this.graphmacd&&this.graphmacd.destroy(),this.graphsignal=this.graphsignal&&this.graphsignal.destroy(),o.seriesTypes.sma.prototype.destroy.apply(this,arguments)}drawGraph(){let s=this,t=s.points,e=s.options,i=s.zones,n={options:{gapSize:e.gapSize}},l=[[],[]],r,h=t.length;for(;h--;)a((r=t[h]).plotMACD)&&l[0].push({plotX:r.plotX,plotY:r.plotMACD,isNull:!a(r.plotMACD)}),a(r.plotSignal)&&l[1].push({plotX:r.plotX,plotY:r.plotSignal,isNull:!a(r.plotMACD)});["macd","signal"].forEach((t,i)=>{s.points=l[i],s.options=p(e[`${t}Line`]?.styles||{},n),s.graph=s[`graph${t}`],s.zones=(s[`${t}Zones`].zones||[]).slice(s[`${t}Zones`].startIndex||0),o.seriesTypes.sma.prototype.drawGraph.call(s),s[`graph${t}`]=s.graph}),s.points=t,s.options=e,s.zones=i}applyZones(){let s=this.zones;this.zones=this.signalZones.zones,o.seriesTypes.sma.prototype.applyZones.call(this),this.graphmacd&&this.options.macdLine.zones.length&&this.graphmacd.hide(),this.zones=s}getValues(s,t){let e=t.longPeriod-t.shortPeriod,i=[],n=[],l=[],p,h,d,c=0,g=[];if(!(s.xData.length<t.longPeriod+t.signalPeriod)){for(d=0,p=o.seriesTypes.ema.prototype.getValues(s,{period:t.shortPeriod,index:t.index}),h=o.seriesTypes.ema.prototype.getValues(s,{period:t.longPeriod,index:t.index}),p=p.values,h=h.values;d<=p.length;d++)a(h[d])&&a(h[d][1])&&a(p[d+e])&&a(p[d+e][0])&&i.push([p[d+e][0],0,null,p[d+e][1]-h[d][1]]);for(d=0;d<i.length;d++)n.push(i[d][0]),l.push([0,null,i[d][3]]);for(d=0,g=(g=o.seriesTypes.ema.prototype.getValues({xData:n,yData:l},{period:t.signalPeriod,index:2})).values;d<i.length;d++)i[d][0]>=g[0][0]&&(i[d][2]=g[c][1],l[d]=[0,g[c][1],i[d][3]],null===i[d][3]?(i[d][1]=0,l[d][0]=0):(i[d][1]=r(i[d][3]-g[c][1]),l[d][0]=r(i[d][3]-g[c][1])),c++);return{values:i,xData:n,yData:l}}}}return h.defaultOptions=p(n.defaultOptions,{params:{shortPeriod:12,longPeriod:26,signalPeriod:9,period:26},signalLine:{zones:[],styles:{lineWidth:1,lineColor:void 0}},macdLine:{zones:[],styles:{lineWidth:1,lineColor:void 0}},threshold:0,groupPadding:.1,pointPadding:.1,crisp:!1,states:{hover:{halo:{size:0}}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>Value: {point.MACD}<br/>Signal: {point.signal}<br/>Histogram: {point.y}<br/>'},dataGrouping:{approximation:"averages"},minPointLength:0}),l(h.prototype,{nameComponents:["longPeriod","shortPeriod","signalPeriod"],pointArrayMap:["y","signal","MACD"],parallelArrays:["x","y","signal","MACD"],pointValKey:"y",markerAttribs:e,getColumnMetrics:s.seriesTypes.column.prototype.getColumnMetrics,crispCol:s.seriesTypes.column.prototype.crispCol,drawPoints:s.seriesTypes.column.prototype.drawPoints}),o.registerSeriesType("macd",h),h}),t(o,"masters/indicators/macd.src.js",[o["Core/Globals.js"]],function(s){return s})});