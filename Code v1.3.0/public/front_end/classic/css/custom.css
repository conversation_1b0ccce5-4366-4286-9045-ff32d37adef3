.h-50px {
  height: 100px;
}
.w-50px {
  width: 140px;
}
.comment-right-margin {
  margin-right: 30px;
}
.iti-container {
  position: relative;
}

.iti {
  width: 100%;
}

.iti__flag-container {
  top: 0;
  bottom: 0;
  left: 0;
  padding: 1px;
}

.phone-input {
  padding-left: 90px !important;
}

.iti__selected-flag {
  width: 85px;
  background-color: transparent !important;
}

.dark .iti__selected-flag {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.dark .iti__country-list {
  background-color: #333;
  border-color: #666;
}

.dark .iti__country {
  color: #fff;
}

.dark .iti__arrow {
  border-top-color: #fff;
}

.dark .iti__country-name,
.dark .iti__dial-code {
  color: #fff;
}

.dark .iti__country.iti__highlight {
  background-color: #555;
}

.dark .phone-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.confirm-button-class {
  background-color: red !important;
  border: none !important;
}

.title-class {
  font-size: 15px !important;
}

.icon-class {
  font-size: 10px !important;
}

.confirm-button-class .swal2-icon svg {
  width: 12px !important;
  height: 12px !important;
}

.swal2-actions .swal2-confirm {
  background-color: #e62323 !important;
  border: none !important;
  box-shadow: none !important;
}

.swal2-actions .swal2-cancel {
  border-color: #e62323 !important;
  box-shadow: none !important;
  border: none !important;
}

.swal2-confirm:focus,
.swal2-cancel:focus {
  box-shadow: none !important;
  border: none !important;
}

.swal2-actions button:hover {
  border: none !important;
}

.custom-profile {
  object-fit: cover;
}

.object-fit-cover {
  object-fit: cover;
}

.dashboard-sidebar {
  height: 50vh;
  position: sticky;
  top: 0;
}

.custom-btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  height: 27px;
}

#suggestions li {
  cursor: pointer;
}

#suggestions {
  list-style-type: none;
  padding: 0;
  margin: 0;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: hidden;
  z-index: 1000;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
}

.suggestion-item i {
  margin-right: 8px;
}

/* Add this to your CSS */
.scrollabl {
  -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
  scrollbar-width: none; /* Hide scrollbar for Firefox */
}

.scrollabl::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome, Safari and Opera */
}
/* Hide scrollbar */
.scrollabl {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollabl::-webkit-scrollbar {
  display: none;
}

/* Comment styles */
.comment-item {
  position: relative;
  padding: 15px;
  border-bottom: 1px solid #eee;
  list-style: none;
}

.comment-container {
  display: flex;
  gap: 15px;
}

.avatar {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
}

.avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.comment-content {
  flex-grow: 1;
}

.comment-info {
  margin-bottom: 8px;
}

.c_name {
  font-weight: bold;
  margin-right: 10px;
}

.c_date {
  color: #666;
  font-size: 0.9em;
  margin-right: 10px;
}

.c_reply a {
  color: #007bff;
  text-decoration: none;
}

.comment {
  margin-top: 5px;
}

/* Replies styling */
.replies-list {
  margin-left: 65px; /* Indent replies */
  padding-left: 0;
  list-style: none;
}

.replies-list .comment-item {
  border-bottom: none;
  padding-bottom: 10px;
}

/* Optional: Add hover effect */
.comment-item:hover {
  background-color: #f8f9fa;
}

.bottom-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  z-index: 1050;
}
.bottom-sheet.show {
  transform: translateY(0);
}
@media (min-width: 1025px) {
  .bottom-sheet {
    display: none;
  }
}

.user-sidebar-img {
  object-fit: cover;
}

.header-img-max-height {
  max-height: 20px;
  max-width: 150px;
}

.max-height-500 {
  max-height: 500px;
  overflow: auto;
}

.custim-left-margin-10 {
  margin-left: 10px !important;
}

.scrollable-container {
  max-height: 150px;
  overflow-y: auto;
  scrollbar-width: none;
}

.scrollable-container::-webkit-scrollbar {
  display: none;
}

/* Header profile dropdown */
.profile-container {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 5px;
}

.dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.dropdown-content a:hover {
  background-color: #f1f1f1;
}

.show {
  display: block;
}
/* Header profile dropdown End*/

.edit-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 95px;
  right: 355px !important;
  z-index: auto;
}

@media (max-width: 1260px) {
  .edit-icon {
    top: 95px;
    right: 320px !important;
  }
}
@media (max-width: 780px) {
  .edit-icon {
    top: 95px;
    right: 255px !important;
  }
}

@media (max-width: 440px) {
  .edit-icon {
    top: 95px;
    right: 80px !important;
  }
}

@media (max-width: 380px) {
  .edit-icon {
    top: 95px;
    right: 62px !important;
  }
}
.pointer-cursor {
  cursor: pointer !important;
}

.logo-image-box {
  height: 75px;
  width: 100%;

  display: flex !important;
  justify-content: center;
  align-items: center;
  padding: 3px;
}
.logo-image-box a {
  width: 100%;
  height: 100%;
}
.logo-image-box img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Story CSs   */
.topic-heading {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  margin-top: 25px;
  border-bottom: 2px solid darkred;
  padding-bottom: 0.5rem;
}
.stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(191.19px, 1fr));
  gap: 0; /* Remove spacing between grid items */
  justify-content: center;
}

.story-card {
  width: 191.19px;
  height: 340px;
  position: relative;
  /* border-radius: 12px; */
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.story-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 1rem;
  color: white;
  transform: translateY(70px);
  transition: transform 0.3s ease;
}

.story-card:hover {
  transform: translateY(-5px);
}

.story-card:hover .story-overlay {
  transform: translateY(0);
}

.story-title {
  font-size: 1.1rem;
  font-weight: bold;
  margin: 0;
}

.story-description {
  font-size: 0.85rem;
  opacity: 0.9;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}



.description-text {
    font-size: 24px;
    background: rgba(0, 0, 0, 0.5);
}

.story-title
{

  padding: 0 0 8px 15px; 
  margin: 20px 0 10px; 
  text-transform: none; 
  clear: both; 
  position: relative; 
  color: var(--black); 
  border-bottom: 1px solid var(--border-color); 
  display: flex; 
  justify-content: space-between; 
  align-items: center;
}


#card_style
{
  height: 410px;
}

#story_image
{
  height: 300px; 
  object-fit: cover;
}

.visual-stories-icon
{
  background: linear-gradient(to top, rgba(73, 73, 73, 0.411), rgba(66, 65, 65, 0))
}

#card_title a
{
  text-decoration: none;
  overflow: hidden;
}

/* amp story css */
.amp-story-logo
{
  position: absolute; 
  top: 10px; 
  left:14%; 
  margin: 9px;
  transform: translateX(-50%); 
  z-index: 10;
  padding: 5px;
}

.card-img-top
{
  height: 300px; 
  object-fit: cover;
}
.amp_story
{
  object-fit: cover;
}

.story-progress-container {
    z-index: 2;
    background: linear-gradient(to top, rgba(0,0,0,0.3), transparent);
}

.progress-segment {
    /* min-height: 2px; */
    border-radius: 2px;
    transition: all 0.3s ease;
}

.progress-segment.active {
    background: white;
    border-top-style: solid !important;
}

.emoji-box {
  position: absolute;
  background-color: #ffffff; /* White background for contrast */
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2); /* Slightly darker shadow for depth */
  z-index: 1000;
  display: flex;
  flex-wrap: wrap; /* Allow emojis to wrap if needed */
}

.emoji {
  cursor: pointer;
  font-size: 23px;
  margin: 5px;
  transition: transform 0.2s;
}

.emoji:hover {
  transform: scale(1.2);
  border-radius: 5px;
}


.avatar-container {
  position: relative; /* Ensure the container is positioned relative to place the emoji on it */
}

.reaction-emoji {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 16px; /* Adjust this size based on your needs */
}

.reaction-icon {
  font-size: 18px; /* Adjust size of the emoji */
  pointer-events: none; /* Ensure the emoji does not interfere with clicking events */
}

.font-size-45{
  font-size: 45px;
}

.iti__country-name{
  color: black;
}



.form-check-input {
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0, 0, 0, .25);
  appearance: none;
  print-color-adjust: exact;
}

.bg-purple {
  background-color: #673ab7 !important;
}

.text-purple {
  color: #673ab7 !important;
}

.btn-purple {
  background-color: #673ab7;
  color: white;
}

.btn-purple:hover {
  background-color: #5e35b1;
  color: white;
}

.btn-outline-purple {
  border-color: #673ab7;
  color: #673ab7;
}

.btn-outline-purple:hover {
  background-color: #673ab7;
  color: white;
}

/* <><><><><><> ADD CSS FOR TRANSLATE DETAIL PAGE  <><><><><><> */
.goog-te-combo {
  position: relative;
  width: 200px;
  padding: 10px;
  margin: 0 auto;
  border-radius: 25px;
  background: #dceaf2;
  color: black;
  outline: none;
  cursor: pointer;
  font-weight: bold;
}

.VIpgJd-ZVi9od-ORHb-OEVmcd.skiptranslate {
  display: none !important;
}

.goog-logo-link {
  display: none !important;
}

.goog-te-gadget {
  color: transparent !important;
}

/*  Add css google translator  */
.VIpgJd-ZVi9od-l4eHX-hSRGPd
{
    display: none !important;

}
/* Hide the Google Translate banner */
.goog-te-banner-frame.skiptranslate {
display: none !important;
}
/* Prevent page from moving down */
body {
top: 0px !important;
}
/* Hide the Google Translate logo */
.goog-logo-link {
display: none !important;
}
/* Hide the "Powered by Google Translate" text */
.goog-te-gadget span {
display: none !important;
}
