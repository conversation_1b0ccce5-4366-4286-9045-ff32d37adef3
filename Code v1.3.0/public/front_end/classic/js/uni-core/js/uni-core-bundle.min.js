/*! UniCore 3.19.1 | https://www.unistudio.co | (c) 2023 - 2024 UniStudio | MIT License */!function(t,e){"object"==typeof exports&&typeof module<"u"?module.exports=e(require("animejs")):"function"==typeof define&&define.amd?define("uikit",["animejs"],e):(t=typeof globalThis<"u"?globalThis:t||self).UniCore=e(t.anime)}(this,(function(t){"use strict";const{hasOwnProperty:e,toString:i}=Object.prototype;function n(t,i){return e.call(t,i)}const s=/\B([A-Z])/g,o=K((t=>t.replace(s,"-$1").toLowerCase())),r=/-(\w)/g,a=K((t=>(t.charAt(0).toLowerCase()+t.slice(1)).replace(r,((t,e)=>e.toUpperCase())))),l=K((t=>t.charAt(0).toUpperCase()+t.slice(1)));function h(t,e){var i;return null==(i=null==t?void 0:t.startsWith)?void 0:i.call(t,e)}function c(t,e){var i;return null==(i=null==t?void 0:t.endsWith)?void 0:i.call(t,e)}function d(t,e){var i;return null==(i=null==t?void 0:t.includes)?void 0:i.call(t,e)}function u(t,e){var i;return null==(i=null==t?void 0:t.findIndex)?void 0:i.call(t,e)}const{isArray:f,from:p}=Array,{assign:m}=Object;function g(t){return"function"==typeof t}function v(t){return null!==t&&"object"==typeof t}function w(t){return"[object Object]"===i.call(t)}function b(t){return v(t)&&t===t.window}function x(t){return 9===S(t)}function $(t){return S(t)>=1}function y(t){return 1===S(t)}function S(t){return!b(t)&&v(t)&&t.nodeType}function I(t){return"boolean"==typeof t}function k(t){return"string"==typeof t}function C(t){return"number"==typeof t}function T(t){return C(t)||k(t)&&!isNaN(t-parseFloat(t))}function E(t){return!(f(t)?t.length:v(t)&&Object.keys(t).length)}function A(t){return void 0===t}function D(t){return I(t)?t:"true"===t||"1"===t||""===t||"false"!==t&&"0"!==t&&t}function P(t){const e=Number(t);return!isNaN(e)&&e}function M(t){return parseFloat(t)||0}function _(t){return B(t)[0]}function B(t){return $(t)?[t]:Array.from(t||[]).filter($)}function O(t){if(b(t))return t;const e=x(t=_(t))?t:null==t?void 0:t.ownerDocument;return(null==e?void 0:e.defaultView)||window}function N(t,e){return t===e||v(t)&&v(e)&&Object.keys(t).length===Object.keys(e).length&&F(t,((t,i)=>t===e[i]))}function z(t,e,i){return t.replace(new RegExp(`${e}|${i}`,"g"),(t=>t===e?i:e))}function H(t){return t[t.length-1]}function F(t,e){for(const i in t)if(!1===e(t[i],i))return!1;return!0}function j(t,e){return t.slice().sort((({[e]:t=0},{[e]:i=0})=>t>i?1:i>t?-1:0))}function L(t,e){return t.reduce(((t,i)=>t+M(g(e)?e(i):i[e])),0)}function W(t,e){const i=new Set;return t.filter((({[e]:t})=>!i.has(t)&&i.add(t)))}function q(t,e){return e.reduce(((e,i)=>({...e,[i]:t[i]})),{})}function V(t,e=0,i=1){return Math.min(Math.max(P(t)||0,e),i)}function R(){}function U(...t){return[["bottom","top"],["right","left"]].every((([e,i])=>Math.min(...t.map((({[e]:t})=>t)))-Math.max(...t.map((({[i]:t})=>t)))>0))}function Y(t,e){return t.x<=e.right&&t.x>=e.left&&t.y<=e.bottom&&t.y>=e.top}function X(t,e,i){const n="width"===e?"height":"width";return{[n]:t[e]?Math.round(i*t[n]/t[e]):t[n],[e]:i}}function J(t,e){t={...t};for(const i in t)t=t[i]>e[i]?X(t,i,e[i]):t;return t}const G={ratio:X,contain:J,cover:function(t,e){t=J(t,e);for(const i in t)t=t[i]<e[i]?X(t,i,e[i]):t;return t}};function Z(t,e,i=0,n=!1){e=B(e);const{length:s}=e;return s?(t=T(t)?P(t):"next"===t?i+1:"previous"===t?i-1:"last"===t?s-1:e.indexOf(_(t)),n?V(t,0,s-1):(t%=s)<0?t+s:t):-1}function K(t){const e=Object.create(null);return(i,...n)=>e[i]||(e[i]=t(i,...n))}function Q(t,e,i){var n;if(v(e))for(const i in e)Q(t,i,e[i]);else{if(A(i))return null==(n=_(t))?void 0:n.getAttribute(e);for(const n of B(t))g(i)&&(i=i.call(n,Q(n,e))),null===i?et(n,e):n.setAttribute(e,i)}}function tt(t,e){return B(t).some((t=>t.hasAttribute(e)))}function et(t,e){B(t).forEach((t=>t.removeAttribute(e)))}function it(t,e){for(const i of[e,`data-${e}`])if(tt(t,i))return Q(t,i)}function nt(t,...e){for(const i of B(t)){const t=ht(e).filter((t=>!at(i,t)));t.length&&i.classList.add(...t)}}function st(t,...e){for(const i of B(t)){const t=ht(e).filter((t=>at(i,t)));t.length&&i.classList.remove(...t)}}function ot(t,e){e=new RegExp(e);for(const i of B(t))i.classList.remove(...p(i.classList).filter((t=>t.match(e))))}function rt(t,e,i){i=ht(i),st(t,e=ht(e).filter((t=>!d(i,t)))),nt(t,i)}function at(t,e){return[e]=ht(e),B(t).some((t=>t.classList.contains(e)))}function lt(t,e,i){const n=ht(e);A(i)||(i=!!i);for(const e of B(t))for(const t of n)e.classList.toggle(t,i)}function ht(t){return t?f(t)?t.map(ht).flat():String(t).split(/[ ,]/).filter(Boolean):[]}const ct={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0};function dt(t){return B(t).some((t=>ct[t.tagName.toLowerCase()]))}function ut(t){return B(t).some((t=>t.offsetWidth||t.offsetHeight||t.getClientRects().length))}const ft="input,select,textarea,button";function pt(t){return B(t).some((t=>bt(t,ft)))}const mt=`${ft},a[href],[tabindex]`;function gt(t){return bt(t,mt)}function vt(t){var e;return null==(e=_(t))?void 0:e.parentElement}function wt(t,e){return B(t).filter((t=>bt(t,e)))}function bt(t,e){return B(t).some((t=>t.matches(e)))}function xt(t,e){var i;return null==(i=_(t))?void 0:i.closest(h(e,">")?e.slice(1):e)}function $t(t,e){return k(e)?!!xt(t,e):_(e).contains(_(t))}function yt(t,e){const i=[];for(;t=vt(t);)(!e||bt(t,e))&&i.push(t);return i}function St(t,e){const i=(t=_(t))?p(t.children):[];return e?wt(i,e):i}function It(t,e){return e?B(t).indexOf(_(e)):St(vt(t)).indexOf(t)}function kt(t){return(t=_(t))&&["origin","pathname","search"].every((e=>t[e]===location[e]))}function Ct(t){if(kt(t)){t=_(t);const e=decodeURIComponent(t.hash).substring(1);return document.getElementById(e)||document.getElementsByName(e)[0]}}function Tt(t,e){return At(t,_t(t,e))}function Et(t,e){return Dt(t,_t(t,e))}function At(t,e){return _(Nt(t,_(e),"querySelector"))}function Dt(t,e){return B(Nt(t,_(e),"querySelectorAll"))}const Pt=/(^|[^\\],)\s*[!>+~-]/,Mt=K((t=>t.match(Pt)));function _t(t,e=document){return k(t)&&Mt(t)||x(e)?e:e.ownerDocument}const Bt=/([!>+~-])(?=\s+[!>+~-]|\s*$)/g,Ot=K((t=>t.replace(Bt,"$1 *")));function Nt(t,e=document,i){if(!t||!k(t))return t;if(t=Ot(t),Mt(t)){const i=Ht(t);t="";for(let n of i){let s=e;if("!"===n[0]){const t=n.substr(1).trim().split(" ");if(s=vt(e).closest(t[0]),n=t.slice(1).join(" ").trim(),!n.length&&1===i.length)return s}if("-"===n[0]){const t=n.substr(1).trim().split(" "),i=(s||e).previousElementSibling;s=bt(i,n.substr(1))?i:null,n=t.slice(1).join(" ")}s&&(t+=`${t?",":""}${Ft(s)} ${n}`)}x(e)||(e=e.ownerDocument)}try{return e[i](t)}catch{return null}}const zt=/.*?[^\\](?![^(]*\))(?:,|$)/g,Ht=K((t=>t.match(zt).map((t=>t.replace(/,$/,"").trim()))));function Ft(t){const e=[];for(;t.parentNode;){const i=Q(t,"id");if(i){e.unshift(`#${jt(i)}`);break}{let{tagName:i}=t;"HTML"!==i&&(i+=`:nth-child(${It(t)+1})`),e.unshift(i),t=t.parentNode}}return e.join(" > ")}function jt(t){return k(t)?CSS.escape(t):""}function Lt(...t){let[e,i,n,s,o=!1]=Ut(t);s.length>1&&(s=function(t){return e=>f(e.detail)?t(e,...e.detail):t(e)}(s)),null!=o&&o.self&&(s=function(t){return function(e){if(e.target===e.currentTarget||e.target===e.current)return t.call(null,e)}}(s)),n&&(s=function(t,e){return i=>{const n=">"===t[0]?Dt(t,i.currentTarget).reverse().find((t=>t.contains(i.target))):i.target.closest(t);n&&(i.current=n,e.call(this,i),delete i.current)}}(n,s));for(const t of i)for(const i of e)i.addEventListener(t,s,o);return()=>Wt(e,i,s,o)}function Wt(...t){let[e,i,,n,s=!1]=Ut(t);for(const t of i)for(const i of e)i.removeEventListener(t,n,s)}function qt(...t){const[e,i,n,s,o=!1,r]=Ut(t),a=Lt(e,i,n,(t=>{const e=!r||r(t);e&&(a(),s(t,e))}),o);return a}function Vt(t,e,i){return Jt(t).every((t=>t.dispatchEvent(Rt(e,!0,!0,i))))}function Rt(t,e=!0,i=!1,n){return k(t)&&(t=new CustomEvent(t,{bubbles:e,cancelable:i,detail:n})),t}function Ut(t){return t[0]=Jt(t[0]),k(t[1])&&(t[1]=t[1].split(" ")),g(t[2])&&t.splice(2,0,!1),t}function Yt(t){return t&&"addEventListener"in t}function Xt(t){return Yt(t)?t:_(t)}function Jt(t){return f(t)?t.map(Xt).filter(Boolean):k(t)?Dt(t):Yt(t)?[t]:B(t)}function Gt(t){return"touch"===t.pointerType||!!t.touches}function Zt(t){var e,i;const{clientX:n,clientY:s}=(null==(e=t.touches)?void 0:e[0])||(null==(i=t.changedTouches)?void 0:i[0])||t;return{x:n,y:s}}const Kt={"animation-iteration-count":!0,"column-count":!0,"fill-opacity":!0,"flex-grow":!0,"flex-shrink":!0,"font-weight":!0,"line-height":!0,opacity:!0,order:!0,orphans:!0,"stroke-dasharray":!0,"stroke-dashoffset":!0,widows:!0,"z-index":!0,zoom:!0};function Qt(t,e,i,n){const s=B(t);for(const t of s)if(k(e)){if(e=te(e),A(i))return getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,T(i)&&!Kt[e]?`${i}px`:i||C(i)?i:"",n)}else{if(f(e)){const i={};for(const n of e)i[n]=Qt(t,n);return i}if(v(e))for(const n in e)Qt(t,n,e[n],i)}return s[0]}const te=K((t=>function(t){if(h(t,"--"))return t;t=o(t);const{style:e}=document.documentElement;if(t in e)return t;for(const i of["webkit","moz"]){const n=`-${i}-${t}`;if(n in e)return n}}(t)));const ee="uc-transition",ie="transitionend",ne="transitioncanceled";const se={start:function(t,e,i=400,n="linear"){return i=Math.round(i),Promise.all(B(t).map((t=>new Promise(((s,o)=>{for(const i in e){const e=Qt(t,i);""===e&&Qt(t,i,e)}const r=setTimeout((()=>Vt(t,ie)),i);qt(t,[ie,ne],(({type:e})=>{clearTimeout(r),st(t,ee),Qt(t,{transitionProperty:"",transitionDuration:"",transitionTimingFunction:""}),e===ne?o():s(t)}),{self:!0}),nt(t,ee),Qt(t,{transitionProperty:Object.keys(e).map(te).join(","),transitionDuration:`${i}ms`,transitionTimingFunction:n,...e})})))))},async stop(t){Vt(t,ie),await Promise.resolve()},async cancel(t){Vt(t,ne),await Promise.resolve()},inProgress:t=>at(t,ee)},oe="uc-animation-",re="animationend",ae="animationcanceled";function le(t,e,i=200,n,s){return Promise.all(B(t).map((t=>new Promise(((o,r)=>{Vt(t,ae);const a=setTimeout((()=>Vt(t,re)),i);qt(t,[re,ae],(({type:e})=>{clearTimeout(a),e===ae?r():o(t),Qt(t,"animationDuration",""),ot(t,`${oe}\\S*`)}),{self:!0}),Qt(t,"animationDuration",`${i}ms`),nt(t,e,oe+(s?"leave":"enter")),h(e,oe)&&(n&&nt(t,`uc-transform-origin-${n}`),s&&nt(t,`${oe}reverse`))})))))}const he=new RegExp(`${oe}(enter|leave)`),ce={in:le,out:(t,e,i,n)=>le(t,e,i,n,!0),inProgress:t=>he.test(Q(t,"class")),cancel(t){Vt(t,ae)}};function de(t,...e){return e.some((e=>{var i;return(null==(i=null==t?void 0:t.tagName)?void 0:i.toLowerCase())===e.toLowerCase()}))}function ue(t){return(t=Te(t)).innerHTML="",t}function fe(t,e){return A(e)?Te(t).innerHTML:me(ue(t),e)}const pe=we("prepend"),me=we("append"),ge=we("before"),ve=we("after");function we(t){return function(e,i){var n;const s=B(k(i)?Ie(i):i);return null==(n=Te(e))||n[t](...s),ke(s)}}function be(t){B(t).forEach((t=>t.remove()))}function xe(t,e){for(e=_(ge(t,e));e.firstElementChild;)e=e.firstElementChild;return me(e,t),e}function $e(t,e){return B(B(t).map((t=>t.hasChildNodes()?xe(p(t.childNodes),e):me(t,e))))}function ye(t){B(t).map(vt).filter(((t,e,i)=>i.indexOf(t)===e)).forEach((t=>t.replaceWith(...t.childNodes)))}const Se=/^<(\w+)\s*\/?>(?:<\/\1>)?$/;function Ie(t){const e=Se.exec(t);if(e)return document.createElement(e[1]);const i=document.createElement("template");return i.innerHTML=t.trim(),ke(i.content.childNodes)}function ke(t){return t.length>1?t:t[0]}function Ce(t,e){if(y(t))for(e(t),t=t.firstElementChild;t;){const i=t.nextElementSibling;Ce(t,e),t=i}}function Te(t,e){return Ae(t)?_(Ie(t)):At(t,e)}function Ee(t,e){return Ae(t)?B(Ie(t)):Dt(t,e)}function Ae(t){return k(t)&&h(t.trim(),"<")}const De={width:["left","right"],height:["top","bottom"]};function Pe(t){const e=y(t)?_(t).getBoundingClientRect():{height:Oe(t),width:Ne(t),top:0,left:0};return{height:e.height,width:e.width,top:e.top,left:e.left,bottom:e.top+e.height,right:e.left+e.width}}function Me(t,e){e&&Qt(t,{left:0,top:0});const i=Pe(t);if(t){const{scrollY:e,scrollX:n}=O(t),s={height:e,width:n};for(const t in De)for(const e of De[t])i[e]+=s[t]}if(!e)return i;for(const n of["left","top"])Qt(t,n,e[n]-i[n])}function _e(t){let{top:e,left:i}=Me(t);const{ownerDocument:{body:n,documentElement:s},offsetParent:o}=_(t);let r=o||s;for(;r&&(r===n||r===s)&&"static"===Qt(r,"position");)r=r.parentNode;if(y(r)){const t=Me(r);e-=t.top+M(Qt(r,"borderTopWidth")),i-=t.left+M(Qt(r,"borderLeftWidth"))}return{top:e-M(Qt(t,"marginTop")),left:i-M(Qt(t,"marginLeft"))}}function Be(t){const e=[(t=_(t)).offsetTop,t.offsetLeft];for(;t=t.offsetParent;)if(e[0]+=t.offsetTop+M(Qt(t,"borderTopWidth")),e[1]+=t.offsetLeft+M(Qt(t,"borderLeftWidth")),"fixed"===Qt(t,"position")){const i=O(t);return e[0]+=i.scrollY,e[1]+=i.scrollX,e}return e}const Oe=ze("height"),Ne=ze("width");function ze(t){const e=l(t);return(i,n)=>{if(A(n)){if(b(i))return i[`inner${e}`];if(x(i)){const t=i.documentElement;return Math.max(t[`offset${e}`],t[`scroll${e}`])}return(n="auto"===(n=Qt(i=_(i),t))?i[`offset${e}`]:M(n)||0)-He(i,t)}return Qt(i,t,n||0===n?+n+He(i,t)+"px":"")}}function He(t,e,i="border-box"){return Qt(t,"boxSizing")===i?L(De[e].map(l),(e=>M(Qt(t,`padding${e}`))+M(Qt(t,`border${e}Width`)))):0}function Fe(t){for(const e in De)for(const i in De[e])if(De[e][i]===t)return De[e][1-i];return t}function je(t,e="width",i=window,n=!1){return k(t)?L(We(t),(t=>{const s=Ve(t);return s?function(t,e){return t*M(e)/100}("vh"===s?Re||(Ue||(Ue=Te("<div>"),Qt(Ue,{height:"100vh",position:"fixed"}),Lt(window,"resize",(()=>Re=null))),me(document.body,Ue),Re=Ue.clientHeight,be(Ue),Re):"vw"===s?Ne(O(i)):n?i[`offset${l(e)}`]:Pe(i)[e],t):t})):M(t)}const Le=/-?\d+(?:\.\d+)?(?:v[wh]|%|px)?/g,We=K((t=>t.toString().replace(/\s/g,"").match(Le)||[])),qe=/(?:v[hw]|%)$/,Ve=K((t=>(t.match(qe)||[])[0]));let Re,Ue;const Ye=typeof window<"u",Xe=Ye&&"rtl"===document.dir,Je=Ye&&"ontouchstart"in window,Ge=Ye&&window.PointerEvent,Ze=Ge?"pointerdown":Je?"touchstart":"mousedown",Ke=Ge?"pointermove":Je?"touchmove":"mousemove",Qe=Ge?"pointerup":Je?"touchend":"mouseup",ti=Ge?"pointerenter":Je?"":"mouseenter",ei=Ge?"pointerleave":Je?"":"mouseleave",ii=Ge?"pointercancel":"touchcancel",ni={reads:[],writes:[],read(t){return this.reads.push(t),ri(),t},write(t){return this.writes.push(t),ri(),t},clear(t){li(this.reads,t),li(this.writes,t)},flush:si};function si(t){ai(ni.reads),ai(ni.writes.splice(0)),ni.scheduled=!1,(ni.reads.length||ni.writes.length)&&ri(t+1)}const oi=4;function ri(t){ni.scheduled||(ni.scheduled=!0,t&&t<oi?Promise.resolve().then((()=>si(t))):requestAnimationFrame((()=>si(1))))}function ai(t){let e;for(;e=t.shift();)try{e()}catch(t){console.error(t)}}function li(t,e){const i=t.indexOf(e);return~i&&t.splice(i,1)}class hi{init(){let t;this.positions=[],this.unbind=Lt(document,"mousemove",(e=>t=Zt(e))),this.interval=setInterval((()=>{t&&(this.positions.push(t),this.positions.length>5&&this.positions.shift())}),50)}cancel(){var t;null==(t=this.unbind)||t.call(this),clearInterval(this.interval)}movesTo(t){if(!this.positions||this.positions.length<2)return!1;const e=Pe(t),{left:i,right:n,top:s,bottom:o}=e,[r]=this.positions,a=H(this.positions),l=[r,a];return!Y(a,e)&&[[{x:i,y:s},{x:n,y:o}],[{x:i,y:o},{x:n,y:s}]].some((t=>{const i=function([{x:t,y:e},{x:i,y:n}],[{x:s,y:o},{x:r,y:a}]){const l=(a-o)*(i-t)-(r-s)*(n-e);if(0===l)return!1;const h=((r-s)*(e-o)-(a-o)*(t-s))/l;return!(h<0)&&{x:t+h*(i-t),y:e+h*(n-e)}}(l,t);return i&&Y(i,e)}))}}function ci(t,e,i={},{intersecting:n=!0}={}){const s=new IntersectionObserver(n?(t,i)=>{t.some((t=>t.isIntersecting))&&e(t,i)}:e,i);for(const e of B(t))s.observe(e);return s}const di=Ye&&window.ResizeObserver;function ui(t,e,i={box:"border-box"}){if(di)return mi(ResizeObserver,t,e,i);const n=[Lt(window,"load resize",e),Lt(document,"loadedmetadata load",e,!0)];return{disconnect:()=>n.map((t=>t()))}}function fi(t){return{disconnect:Lt([window,window.visualViewport],"resize",t)}}function pi(t,e,i){return mi(MutationObserver,t,e,i)}function mi(t,e,i,n){const s=new t(i);for(const t of B(e))s.observe(t,n);return s}function gi(t){$i(t)&&Ii(t,{func:"playVideo",method:"play"}),xi(t)&&t.play()}function vi(t){$i(t)&&Ii(t,{func:"pauseVideo",method:"pause"}),xi(t)&&t.pause()}function wi(t){$i(t)&&Ii(t,{func:"mute",method:"setVolume",value:0}),xi(t)&&(t.muted=!0)}function bi(t){return xi(t)||$i(t)}function xi(t){return de(t,"video")}function $i(t){return de(t,"iframe")&&(yi(t)||Si(t))}function yi(t){return!!t.src.match(/\/\/.*?youtube(-nocookie)?\.[a-z]+\/(watch\?v=[^&\s]+|embed)|youtu\.be\/.*/)}function Si(t){return!!t.src.match(/vimeo\.com\/video\/.*/)}async function Ii(t,e){await function(t){if(t[Ci])return t[Ci];const e=yi(t),i=Si(t),n=++Ti;let s;return t[Ci]=new Promise((o=>{e&&qt(t,"load",(()=>{const e=()=>ki(t,{event:"listening",id:n});s=setInterval(e,100),e()})),qt(window,"message",o,!1,(({data:t})=>{try{return t=JSON.parse(t),e&&(null==t?void 0:t.id)===n&&"onReady"===t.event||i&&Number(null==t?void 0:t.player_id)===n}catch{}})),t.src=`${t.src}${d(t.src,"?")?"&":"?"}${e?"enablejsapi=1":`api=1&player_id=${n}`}`})).then((()=>clearInterval(s)))}(t),ki(t,e)}function ki(t,e){t.contentWindow.postMessage(JSON.stringify({event:"command",...e}),"*")}const Ci="_ukPlayer";let Ti=0;function Ei(t,{offset:e=0}={}){const i=ut(t)?Di(t,!1,["hidden"]):[];return i.reduce(((n,s,o)=>{const{scrollTop:r,scrollHeight:a,offsetHeight:l}=s,h=_i(s),c=a-h.height,{height:d,top:u}=i[o-1]?_i(i[o-1]):Me(t);let f=Math.ceil(u-h.top-e+r);return e>0&&l<d+e?f+=e:e=0,f>c?(e-=f-c,f=c):f<0&&(e-=f,f=0),()=>function(t,e,n,s){return new Promise((o=>{const r=t.scrollTop,a=function(t){return 40*Math.pow(t,.375)}(Math.abs(e)),l=Date.now(),h=zi(t)===t,c=Me(n).top+(h?0:r);let d=0,u=15;!function f(){const p=function(t){return.5*(1-Math.cos(Math.PI*t))}(V((Date.now()-l)/a));let m=0;if(i[0]===t&&r+e<s){m=Me(n).top+(h?0:t.scrollTop)-c;const e=Bi(n);m-=e?Me(e).height:0}t.scrollTop=Math[e+m>0?"max":"min"](t.scrollTop,r+(e+m)*p),1!==p||d!==m&&u--?(d=m,requestAnimationFrame(f)):o()}()}))}(s,f-r,t,c).then(n)}),(()=>Promise.resolve()))()}function Ai(t,e=0,i=0){if(!ut(t))return 0;const n=Pi(t,!0),{scrollHeight:s,scrollTop:o}=n,{height:r}=_i(n),a=s-r,l=Be(t)[0]-Be(n)[0],h=Math.max(0,l-r+e),c=Math.min(a,l+t.offsetHeight-i);return h<c?V((o-h)/(c-h)):1}function Di(t,e=!1,i=[]){const n=zi(t);let s=yt(t).reverse();s=s.slice(s.indexOf(n)+1);const o=u(s,(t=>"fixed"===Qt(t,"position")));return~o&&(s=s.slice(o)),[n].concat(s.filter((t=>Qt(t,"overflow").split(" ").some((t=>d(["auto","scroll",...i],t)))&&(!e||t.scrollHeight>_i(t).height)))).reverse()}function Pi(...t){return Di(...t)[0]}function Mi(t){return Di(t,!1,["hidden","clip"])}function _i(t){const e=O(t);let i=t===zi(t)?e:t;if(b(i)&&e.visualViewport){let{height:t,width:i,scale:n,pageTop:s,pageLeft:o}=e.visualViewport;return t=Math.round(t*n),i=Math.round(i*n),{height:t,width:i,top:s,left:o,bottom:s+t,right:o+i}}let n=Me(i);if("inline"===Qt(i,"display"))return n;for(let[e,s,o,r]of[["width","x","left","right"],["height","y","top","bottom"]]){b(i)?i=t.ownerDocument:n[o]+=M(Qt(i,`border-${o}-width`));const a=n[e]%1;n[e]=n[s]=i[`client${l(e)}`]-(a?a<.5?-a:1-a:0),n[r]=n[e]+n[o]}return n}function Bi(t){const{left:e,width:i,top:n}=Pe(t);return t.ownerDocument.elementsFromPoint(e+i/2,n).find((e=>!e.contains(t)&&!at(e,"uc-togglable-leave")&&(Ni(e,"fixed")&&Oi(yt(t).reverse().find((t=>!t.contains(e)&&!Ni(t,"static"))))<Oi(e)||Ni(e,"sticky")&&vt(e).contains(t))))}function Oi(t){return M(Qt(t,"zIndex"))}function Ni(t,e){return Qt(t,"position")===e}function zi(t){return O(t).document.scrollingElement}const Hi=[["width","x","left","right"],["height","y","top","bottom"]];function Fi(t,e,i){i={attach:{element:["left","top"],target:["left","top"],...i.attach},offset:[0,0],placement:[],...i},f(e)||(e=[e,e]),Me(t,ji(t,e,i))}function ji(t,e,i){const n=Li(t,e,i),{boundary:s,viewportOffset:o=0,placement:r}=i;let a=n;for(const[l,[h,,c,d]]of Object.entries(Hi)){const u=Vi(t,e[l],o,s,l);if(Xi(n,u,l))continue;let f=0;if("flip"===r[l]){const s=i.attach.target[l];if(s===d&&n[d]<=u[d]||s===c&&n[c]>=u[c])continue;f=Ji(t,e,i,l)[c]-n[c];const r=Ri(t,e[l],o,l);if(!Xi(Wi(n,f,l),r,l)){if(Xi(n,r,l))continue;if(i.recursion)return!1;const s=Gi(t,e,i);if(s&&Xi(s,r,1-l))return s;continue}}else if("shift"===r[l]){const t=Me(e[l]),{offset:s}=i;f=V(V(n[c],u[c],u[d]-n[h]),t[c]-n[h]+s[l],t[d]-s[l])-n[c]}a=Wi(a,f,l)}return a}function Li(t,e,i){let{attach:n,offset:s}={attach:{element:["left","top"],target:["left","top"],...i.attach},offset:[0,0],...i},o=Me(t);for(const[t,[i,,r,a]]of Object.entries(Hi)){const l=n.target[t]===n.element[t]?_i(e[t]):Me(e[t]);o=Wi(o,l[r]-o[r]+qi(n.target[t],a,l[i])-qi(n.element[t],a,o[i])+ +s[t],t)}return o}function Wi(t,e,i){const[,n,s,o]=Hi[i],r={...t};return r[s]=t[n]=t[s]+e,r[o]+=e,r}function qi(t,e,i){return"center"===t?i/2:t===e?i:0}function Vi(t,e,i,n,s){let o=Yi(...Ui(t,e).map(_i));return i&&(o[Hi[s][2]]+=i,o[Hi[s][3]]-=i),n&&(o=Yi(o,Me(f(n)?n[s]:n))),o}function Ri(t,e,i,n){const[s,o,r,a]=Hi[n],[h]=Ui(t,e),c=_i(h);return["auto","scroll"].includes(Qt(h,`overflow-${o}`))&&(c[r]-=h[`scroll${l(r)}`],c[a]=c[r]+h[`scroll${l(s)}`]),c[r]+=i,c[a]-=i,c}function Ui(t,e){return Mi(e).filter((e=>e.contains(t)))}function Yi(...t){let e={};for(const i of t)for(const[,,t,n]of Hi)e[t]=Math.max(e[t]||0,i[t]),e[n]=Math.min(...[e[n],i[n]].filter(Boolean));return e}function Xi(t,e,i){const[,,n,s]=Hi[i];return t[n]>=e[n]&&t[s]<=e[s]}function Ji(t,e,{offset:i,attach:n},s){return Li(t,e,{attach:{element:Zi(n.element,s),target:Zi(n.target,s)},offset:Qi(i,s)})}function Gi(t,e,i){return ji(t,e,{...i,attach:{element:i.attach.element.map(Ki).reverse(),target:i.attach.target.map(Ki).reverse()},offset:i.offset.reverse(),placement:i.placement.reverse(),recursion:!0})}function Zi(t,e){const i=[...t],n=Hi[e].indexOf(t[e]);return~n&&(i[e]=Hi[e][1-n%2+2]),i}function Ki(t){for(let e=0;e<Hi.length;e++){const i=Hi[e].indexOf(t);if(~i)return Hi[1-e][i%2+2]}}function Qi(t,e){return(t=[...t])[e]*=-1,t}var tn=Object.freeze({__proto__:null,$:Te,$$:Ee,Animation:ce,Dimensions:G,MouseTracker:hi,Transition:se,addClass:nt,after:ve,append:me,apply:Ce,assign:m,attr:Q,before:ge,boxModelAdjust:He,camelize:a,children:St,clamp:V,closest:xt,createEvent:Rt,css:Qt,data:it,defineComponent:function(t){return t},dimensions:Pe,each:F,empty:ue,endsWith:c,escape:jt,fastdom:ni,filter:wt,find:At,findAll:Dt,findIndex:u,flipPosition:Fe,fragment:Ie,getCoveringElement:Bi,getEventPos:Zt,getIndex:Z,getTargetedElement:Ct,hasAttr:tt,hasClass:at,hasOwn:n,hasTouch:Je,height:Oe,html:fe,hyphenate:o,inBrowser:Ye,includes:d,index:It,intersectRect:U,isArray:f,isBoolean:I,isDocument:x,isElement:y,isEmpty:E,isEqual:N,isFocusable:gt,isFunction:g,isInView:function(t,e=0,i=0){return!!ut(t)&&U(...Mi(t).map((t=>{const{top:n,left:s,bottom:o,right:r}=_i(t);return{top:n-e,left:s-i,bottom:o+e,right:r+i}})).concat(Me(t)))},isInput:pt,isNode:$,isNumber:C,isNumeric:T,isObject:v,isPlainObject:w,isRtl:Xe,isSameSiteAnchor:kt,isString:k,isTag:de,isTouch:Gt,isUndefined:A,isVideo:bi,isVisible:ut,isVoidElement:dt,isWindow:b,last:H,matches:bt,memoize:K,mute:wi,noop:R,observeIntersection:ci,observeMutation:pi,observeResize:ui,observeViewportResize:fi,off:Wt,offset:Me,offsetPosition:Be,offsetViewport:_i,on:Lt,once:qt,overflowParents:Mi,parent:vt,parents:yt,pause:vi,pick:q,play:gi,pointInRect:Y,pointerCancel:ii,pointerDown:Ze,pointerEnter:ti,pointerLeave:ei,pointerMove:Ke,pointerUp:Qe,position:_e,positionAt:Fi,prepend:pe,propName:te,query:Tt,queryAll:Et,ready:function(t){"loading"===document.readyState?qt(document,"DOMContentLoaded",t):t()},remove:be,removeAttr:et,removeClass:st,removeClasses:ot,replaceClass:rt,scrollIntoView:Ei,scrollParent:Pi,scrollParents:Di,scrolledOver:Ai,selFocusable:mt,selInput:ft,sortBy:j,startsWith:h,sumBy:L,swap:z,toArray:p,toBoolean:D,toEventTargets:Jt,toFloat:M,toNode:_,toNodes:B,toNumber:P,toPx:je,toWindow:O,toggleClass:lt,trigger:Vt,ucfirst:l,uniqueBy:W,unwrap:ye,width:Ne,within:$t,wrapAll:xe,wrapInner:$e}),en={connected(){nt(this.$el,this.$options.id)}};const nn=["days","hours","minutes","seconds"];var sn={mixins:[en],props:{date:String,clsWrapper:String,role:String},data:{date:"",clsWrapper:".uc-countdown-%unit%",role:"timer"},connected(){Q(this.$el,"role",this.role),this.date=M(Date.parse(this.$props.date)),this.end=!1,this.start()},disconnected(){this.stop()},events:{name:"visibilitychange",el:()=>document,handler(){document.hidden?this.stop():this.start()}},methods:{start(){this.stop(),this.update(),this.timer||(Vt(this.$el,"countdownstart"),this.timer=setInterval(this.update,1e3))},stop(){this.timer&&(clearInterval(this.timer),Vt(this.$el,"countdownstop"),this.timer=null)},update(){const t=function(t){const e=Math.max(0,t-Date.now())/1e3;return{total:e,seconds:e%60,minutes:e/60%60,hours:e/60/60%24,days:e/60/60/24}}(this.date);t.total||(this.stop(),this.end||(Vt(this.$el,"countdownend"),this.end=!0));for(const e of nn){const i=Te(this.clsWrapper.replace("%unit%",e),this.$el);if(!i)continue;let n=String(Math.trunc(t[e]));n=n.length<2?`0${n}`:n,i.textContent!==n&&(n=n.split(""),n.length!==i.children.length&&fe(i,n.map((()=>"<span></span>")).join("")),n.forEach(((t,e)=>i.children[e].textContent=t)))}}}};const on={};function rn(t,e,i){return on.computed(g(t)?t.call(i,i):t,g(e)?e.call(i,i):e)}function an(t,e){return t=t&&!f(t)?[t]:t,e?t?t.concat(e):f(e)?e:[e]:t}function ln(t,e){return A(e)?t:e}function hn(t,e,i){const s={};if(g(e)&&(e=e.options),e.extends&&(t=hn(t,e.extends,i)),e.mixins)for(const n of e.mixins)t=hn(t,n,i);for(const e in t)o(e);for(const i in e)n(t,i)||o(i);function o(n){s[n]=(on[n]||ln)(t[n],e[n],i)}return s}function cn(t,e=[]){try{return t?h(t,"{")?JSON.parse(t):e.length&&!d(t,":")?{[e[0]]:t}:t.split(";").reduce(((t,e)=>{const[i,n]=e.split(/:(.*)/);return i&&!A(n)&&(t[i.trim()]=n.trim()),t}),{}):{}}catch{return{}}}function dn(t,e){return t===Boolean?D(e):t===Number?P(e):"list"===t?function(t){return f(t)?t:k(t)?t.split(un).map((t=>T(t)?P(t):D(t.trim()))):[t]}(e):t===Object&&k(e)?cn(e):t?t(e):e}on.events=on.watch=on.observe=on.created=on.beforeConnect=on.connected=on.beforeDisconnect=on.disconnected=on.destroy=an,on.args=function(t,e){return!1!==e&&an(e||t)},on.update=function(t,e){return j(an(t,g(e)?{read:e}:e),"order")},on.props=function(t,e){if(f(e)){const t={};for(const i of e)t[i]=String;e=t}return on.methods(t,e)},on.computed=on.methods=function(t,e){return e?t?{...t,...e}:e:t},on.i18n=on.data=function(t,e,i){return i?rn(t,e,i):e?t?function(i){return rn(t,e,i)}:e:t};const un=/,(?![^(]*\))/;function fn(t,e="update"){t._connected&&t._updates.length&&(t._queued||(t._queued=new Set,ni.read((()=>{t._connected&&function(t,e){for(const{read:i,write:n,events:s=[]}of t._updates){if(!e.has("update")&&!s.some((t=>e.has(t))))continue;let o;i&&(o=i.call(t,t._data,e),o&&w(o)&&m(t._data,o)),n&&!1!==o&&ni.write((()=>{t._connected&&n.call(t,t._data,e)}))}}(t,t._queued),delete t._queued}))),t._queued.add(e.type||e))}function pn(t){return $n(ui,t,"resize")}function mn(t){return $n(ci,t)}function gn(t){return $n(pi,t)}function vn(t={}){return mn({handler:function(e,i){const{targets:n=this.$el,preload:s=5}=t;for(const t of B(g(n)?n(this):n))Ee('[loading="lazy"]',t).slice(0,s-1).forEach((t=>et(t,"loading")));for(const t of e.filter((({isIntersecting:t})=>t)).map((({target:t})=>t)))i.unobserve(t)},...t})}function wn(t){return $n(((t,e)=>fi(e)),t,"resize")}function bn(t){return $n(((t,e)=>({disconnect:Lt(yn(t),"scroll",e,{passive:!0})})),t,"scroll")}function xn(t){return{observe:(t,e)=>({observe:R,unobserve:R,disconnect:Lt(t,Ze,e,{passive:!0})}),handler(t){if(!Gt(t))return;const e=Zt(t),i="tagName"in t.target?t.target:vt(t.target);qt(document,`${Qe} ${ii} scroll`,(t=>{const{x:n,y:s}=Zt(t);("scroll"!==t.type&&i&&n&&Math.abs(e.x-n)>100||s&&Math.abs(e.y-s)>100)&&setTimeout((()=>{Vt(i,"swipe"),Vt(i,`swipe${function(t,e,i,n){return Math.abs(t-i)>=Math.abs(e-n)?t-i>0?"Left":"Right":e-n>0?"Up":"Down"}(e.x,e.y,n,s)}`)}))}))},...t}}function $n(t,e,i){return{observe:t,handler(){fn(this,i)},...e}}function yn(t){return B(t).map((t=>{const{ownerDocument:e}=t,i=Pi(t,!0);return i===e.scrollingElement?e:i}))}var Sn={props:{margin:String,firstColumn:Boolean},data:{margin:"uc-margin-small-top",firstColumn:"uc-first-column"},observe:[gn({options:{childList:!0,attributes:!0,attributeFilter:["style"]}}),pn({target:({$el:t})=>[t,...St(t)]})],update:{read(){return{rows:In(p(this.$el.children))}},write({rows:t}){for(const e of t)for(const i of e)lt(i,this.margin,t[0]!==e),lt(i,this.firstColumn,e[Xe?e.length-1:0]===i)},events:["resize"]}};function In(t){const e=[[]],i=t.some(((e,i)=>i&&t[i-1].offsetParent!==e.offsetParent));for(const n of t){if(!ut(n))continue;const t=kn(n,i);for(let s=e.length-1;s>=0;s--){const o=e[s];if(!o[0]){o.push(n);break}const r=kn(o[0],i);if(t.top>=r.bottom-1&&t.top!==r.top){e.push([n]);break}if(t.bottom-1>r.top||t.top===r.top){let e=o.length-1;for(;e>=0;e--){const n=kn(o[e],i);if(t.left>=n.left)break}o.splice(e+1,0,n);break}if(0===s){e.unshift([n]);break}}}return e}function kn(t,e=!1){let{offsetTop:i,offsetLeft:n,offsetHeight:s,offsetWidth:o}=t;return e&&([i,n]=Be(t)),{top:i,left:n,bottom:i+s,right:n+o}}const Cn="uc-transition-leave",Tn="uc-transition-enter";function En(t,e,i,n=0){const s=An(e,!0),o={opacity:1},r={opacity:0},a=t=>()=>s===An(e)?t():Promise.reject(),l=a((async()=>{nt(e,Cn),await Promise.all(Pn(e).map(((t,e)=>new Promise((s=>setTimeout((()=>se.start(t,r,i/2,"ease").then(s)),e*n)))))),st(e,Cn)})),h=a((async()=>{const a=Oe(e);nt(e,Tn),t(),Qt(St(e),{opacity:0}),await new Promise((t=>requestAnimationFrame(t)));const l=St(e),h=Oe(e);Qt(e,"alignContent","flex-start"),Oe(e,a);const c=Pn(e);Qt(l,r);const d=c.map((async(t,e)=>{await function(t){return new Promise((e=>setTimeout(e,t)))}(e*n),await se.start(t,o,i/2,"ease")}));a!==h&&d.push(se.start(e,{height:h},i/2+c.length*n,"ease")),await Promise.all(d).then((()=>{st(e,Tn),s===An(e)&&(Qt(e,{height:"",alignContent:""}),Qt(l,{opacity:""}),delete e.dataset.transition)}))}));return at(e,Cn)?Dn(e).then(h):at(e,Tn)?Dn(e).then(l).then(h):l().then(h)}function An(t,e){return e&&(t.dataset.transition=1+An(t)),P(t.dataset.transition)||0}function Dn(t){return Promise.all(St(t).filter(se.inProgress).map((t=>new Promise((e=>qt(t,"transitionend transitioncanceled",e))))))}function Pn(t){return In(St(t)).flat().filter((t=>ut(t)))}async function Mn(t,e,i){await On();let n=St(e);const s=n.map((t=>_n(t,!0))),o={...Qt(e,["height","padding"]),display:"block"};await Promise.all(n.concat(e).map(se.cancel)),t(),n=n.concat(St(e).filter((t=>!d(n,t)))),await Promise.resolve(),ni.flush();const r=Q(e,"style"),a=Qt(e,["height","padding"]),[l,h]=function(t,e,i){const n=e.map(((t,e)=>!(!vt(t)||!(e in i))&&(i[e]?ut(t)?Bn(t):{opacity:0}:{opacity:ut(t)?1:0}))),s=n.map(((n,s)=>{const o=vt(e[s])===t&&(i[s]||_n(e[s]));if(!o)return!1;if(n){if(!("opacity"in n)){const{opacity:t}=o;t%1?n.opacity=1:delete o.opacity}}else delete o.opacity;return o}));return[n,s]}(e,n,s),c=n.map((t=>({style:Q(t,"style")})));n.forEach(((t,e)=>h[e]&&Qt(t,h[e]))),Qt(e,o),Vt(e,"scroll"),ni.flush(),await On();const u=n.map(((t,n)=>vt(t)===e&&se.start(t,l[n],i,"ease"))).concat(se.start(e,a,i,"ease"));try{await Promise.all(u),n.forEach(((t,i)=>{Q(t,c[i]),vt(t)===e&&Qt(t,"display",0===l[i].opacity?"none":"")})),Q(e,"style",r)}catch{Q(n,"style",""),function(t,e){for(const i in e)Qt(t,i,"")}(e,o)}}function _n(t,e){const i=Qt(t,"zIndex");return!!ut(t)&&{display:"",opacity:e?Qt(t,"opacity"):"0",pointerEvents:"none",position:"absolute",zIndex:"auto"===i?It(t):i,...Bn(t)}}function Bn(t){const{height:e,width:i}=Me(t);return{height:e,width:i,transform:"",..._e(t),...Qt(t,["marginTop","marginLeft"])}}function On(){return new Promise((t=>requestAnimationFrame(t)))}var Nn={props:{duration:Number,animation:Boolean},data:{duration:150,animation:"slide"},methods:{animate(t,e=this.$el){const i=this.animation;return("fade"===i?En:"delayed-fade"===i?(...t)=>En(...t,40):i?Mn:()=>(t(),Promise.resolve()))(t,e,this.duration).catch(R)}}};const zn=9,Hn=27,Fn=32,jn=35,Ln=36,Wn=37,qn=38,Vn=39,Rn=40;var Un={mixins:[Nn],args:"target",props:{target:String,selActive:Boolean},data:{target:"",selActive:!1,attrItem:"uc-filter-control",cls:"uc-active",duration:250},computed:{children:({target:t},e)=>Ee(`${t} > *`,e),toggles:({attrItem:t},e)=>Ee(`[${t}],[data-${t}]`,e)},watch:{toggles(t){this.updateState();const e=Ee(this.selActive,this.$el);for(const i of t){!1!==this.selActive&&lt(i,this.cls,d(e,i));const t=Gn(i);de(t,"a")&&Q(t,"role","button")}},children(t,e){e&&this.updateState()}},events:{name:"click keydown",delegate(){return`[${this.attrItem}],[data-${this.attrItem}]`},handler(t){"keydown"===t.type&&t.keyCode!==Fn||t.target.closest("a,button")&&(t.preventDefault(),this.apply(t.current))}},methods:{apply(t){const e=this.getState(),i=Xn(t,this.attrItem,this.getState());(function(t,e){return["filter","sort"].every((i=>N(t[i],e[i])))})(e,i)||this.setState(i)},getState(){return this.toggles.filter((t=>at(t,this.cls))).reduce(((t,e)=>Xn(e,this.attrItem,t)),{filter:{"":""},sort:[]})},async setState(t,e=!0){t={filter:{"":""},sort:[],...t},Vt(this.$el,"beforeFilter",[this,t]);for(const e of this.toggles)lt(e,this.cls,Jn(e,this.attrItem,t));await Promise.all(Ee(this.target,this.$el).map((i=>{const n=()=>{(function(t,e,i){const n=function({filter:t}){let e="";return F(t,(t=>e+=t||"")),e}(t);i.forEach((t=>Qt(t,"display",n&&!bt(t,n)?"none":"")));const[s,o]=t.sort;if(s){const t=function(t,e,i){return[...t].sort(((t,n)=>it(t,e).localeCompare(it(n,e),void 0,{numeric:!0})*("asc"===i||-1)))}(i,s,o);N(t,i)||me(e,t)}})(t,i,St(i)),this.$update(this.$el)};return e?this.animate(n,i):n()}))),Vt(this.$el,"afterFilter",[this])},updateState(){ni.write((()=>this.setState(this.getState(),!1)))}}};function Yn(t,e){return cn(it(t,e),["filter"])}function Xn(t,e,i){const{filter:n,group:s,sort:o,order:r="asc"}=Yn(t,e);return(n||A(o))&&(s?n?(delete i.filter[""],i.filter[s]=n):(delete i.filter[s],(E(i.filter)||""in i.filter)&&(i.filter={"":n||""})):i.filter={"":n||""}),A(o)||(i.sort=[o,r]),i}function Jn(t,e,{filter:i={"":""},sort:[n,s]}){const{filter:o="",group:r="",sort:a,order:l="asc"}=Yn(t,e);return A(a)?r in i&&o===i[r]||!o&&r&&!(r in i)&&!i[""]:n===a&&s===l}function Gn(t){return Te("a,button",t)||t}let Zn;function Kn(t){const e=Lt(t,"touchstart",(e=>{if(1!==e.targetTouches.length||bt(e.target,'input[type="range"'))return;let i=Zt(e).y;const n=Lt(t,"touchmove",(e=>{const n=Zt(e).y;n!==i&&(i=n,Di(e.target).some((e=>{if(!t.contains(e))return!1;let{scrollHeight:i,clientHeight:n}=e;return n<i}))||e.preventDefault())}),{passive:!1});qt(t,"scroll touchend touchcanel",n,{capture:!0})}));if(Zn)return e;Zn=!0;const{scrollingElement:i}=document;return Qt(i,{overflowY:CSS.supports("overflow","clip")?"clip":"hidden",touchAction:"none",paddingRight:Ne(window)-i.clientWidth||""}),()=>{Zn=!1,e(),Qt(i,{overflowY:"",touchAction:"",paddingRight:""})}}var Qn={props:{container:Boolean},data:{container:!0},computed:{container({container:t}){return!0===t&&this.$container||t&&Te(t)}}},ts={props:{cls:Boolean,animation:"list",duration:Number,velocity:Number,origin:String,transition:String},data:{cls:!1,animation:[!1],duration:200,velocity:.2,origin:!1,transition:"ease",clsEnter:"uc-togglable-enter",clsLeave:"uc-togglable-leave"},computed:{hasAnimation:({animation:t})=>!!t[0],hasTransition:({animation:t})=>["slide","reveal"].some((e=>h(t[0],e)))},methods:{async toggleElement(t,e,i){try{return await Promise.all(B(t).map((t=>{const n=I(e)?e:!this.isToggled(t);if(!Vt(t,"before"+(n?"show":"hide"),[this]))return Promise.reject();const s=(g(i)?i:!1!==i&&this.hasAnimation?this.hasTransition?is:ns:es)(t,n,this),o=n?this.clsEnter:this.clsLeave;nt(t,o),Vt(t,n?"show":"hide",[this]);const r=()=>{st(t,o),Vt(t,n?"shown":"hidden",[this])};return s?s.then(r,(()=>(st(t,o),Promise.reject()))):r()}))),!0}catch{return!1}},isToggled(t=this.$el){return!!at(t=_(t),this.clsEnter)||!at(t,this.clsLeave)&&(this.cls?at(t,this.cls.split(" ")[0]):ut(t))},_toggle(t,e){if(!t)return;let i;e=!!e,this.cls?(i=d(this.cls," ")||e!==at(t,this.cls),i&&lt(t,this.cls,d(this.cls," ")?void 0:e)):(i=e===t.hidden,i&&(t.hidden=!e)),Ee("[autofocus]",t).some((t=>ut(t)?t.focus()||!0:t.blur())),i&&Vt(t,"toggled",[e,this])}}};function es(t,e,{_toggle:i}){return ce.cancel(t),se.cancel(t),i(t,e)}async function is(t,e,{animation:i,duration:n,velocity:s,transition:o,_toggle:r}){var a;const[l="reveal",h="top"]=(null==(a=i[0])?void 0:a.split("-"))||[],c=[["left","right"],["top","bottom"]],u=c[d(c[0],h)?0:1],f=u[1]===h,p=["width","height"][c.indexOf(u)],m=`margin-${u[0]}`,g=`margin-${h}`;let v=Pe(t)[p];const w=se.inProgress(t);await se.cancel(t),e&&r(t,!0);const b=Object.fromEntries(["padding","border","width","height","minWidth","minHeight","overflowY","overflowX",m,g].map((e=>[e,t.style[e]]))),x=Pe(t),$=M(Qt(t,m)),y=M(Qt(t,g)),S=x[p]+y;!w&&!e&&(v+=y);const[I]=$e(t,"<div>");Qt(I,{boxSizing:"border-box",height:x.height,width:x.width,...Qt(t,["overflow","padding","borderTop","borderRight","borderBottom","borderLeft","borderImage",g])}),Qt(t,{padding:0,border:0,minWidth:0,minHeight:0,[g]:0,width:x.width,height:x.height,overflow:"hidden",[p]:v});const k=v/S;n=(s*S+n)*(e?1-k:k);const C={[p]:e?S:0};f&&(Qt(t,m,S-v+$),C[m]=e?$:S+$),!f^"reveal"===l&&(Qt(I,m,-S+v),se.start(I,{[m]:e?0:-S},n,o));try{await se.start(t,C,n,o)}finally{Qt(t,b),ye(I.firstChild),e||r(t,!1)}}function ns(t,e,i){const{animation:n,duration:s,_toggle:o}=i;return e?(o(t,!0),ce.in(t,n[0],s,i.origin)):ce.out(t,n[1]||n[0],s,i.origin).then((()=>o(t,!1)))}const ss=[];var os={mixins:[en,Qn,ts],props:{selPanel:String,selClose:String,escClose:Boolean,bgClose:Boolean,stack:Boolean,role:String},data:{cls:"uc-open",escClose:!0,bgClose:!0,overlay:!0,stack:!1,role:"dialog"},computed:{panel:({selPanel:t},e)=>Te(t,e),transitionElement(){return this.panel},bgClose({bgClose:t}){return t&&this.panel}},connected(){Q(this.panel||this.$el,"role",this.role),this.overlay&&Q(this.panel||this.$el,"aria-modal",!0)},beforeDisconnect(){d(ss,this)&&this.toggleElement(this.$el,!1,!1)},events:[{name:"click",delegate(){return`${this.selClose},a[href*="#"]`},handler(t){const{current:e,defaultPrevented:i}=t,{hash:n}=e;!i&&n&&kt(e)&&!this.$el.contains(Te(n))?this.hide():bt(e,this.selClose)&&(t.preventDefault(),this.hide())}},{name:"toggle",self:!0,handler(t){t.defaultPrevented||(t.preventDefault(),this.isToggled()===d(ss,this)&&this.toggle())}},{name:"beforeshow",self:!0,handler(t){if(d(ss,this))return!1;!this.stack&&ss.length?(Promise.all(ss.map((t=>t.hide()))).then(this.show),t.preventDefault()):ss.push(this)}},{name:"show",self:!0,handler(){this.stack&&Qt(this.$el,"zIndex",M(Qt(this.$el,"zIndex"))+ss.length);const t=[this.overlay&&as(this),this.overlay&&Kn(this.$el),this.bgClose&&ls(this),this.escClose&&hs(this)];qt(this.$el,"hidden",(()=>t.forEach((t=>t&&t()))),{self:!0}),nt(document.documentElement,this.clsPage)}},{name:"shown",self:!0,handler(){gt(this.$el)||Q(this.$el,"tabindex","-1"),bt(this.$el,":focus-within")||this.$el.focus()}},{name:"hidden",self:!0,handler(){d(ss,this)&&ss.splice(ss.indexOf(this),1),Qt(this.$el,"zIndex",""),ss.some((t=>t.clsPage===this.clsPage))||st(document.documentElement,this.clsPage)}}],methods:{toggle(){return this.isToggled()?this.hide():this.show()},show(){return this.container&&vt(this.$el)!==this.container?(me(this.container,this.$el),new Promise((t=>requestAnimationFrame((()=>this.show().then(t)))))):this.toggleElement(this.$el,!0,rs)},hide(){return this.toggleElement(this.$el,!1,rs)}}};function rs(t,e,{transitionElement:i,_toggle:n}){return new Promise(((s,o)=>qt(t,"show hide",(()=>{var r;null==(r=t._reject)||r.call(t),t._reject=o,n(t,e);const a=qt(i,"transitionstart",(()=>{qt(i,"transitionend transitioncancel",s,{self:!0}),clearTimeout(l)}),{self:!0}),l=setTimeout((()=>{a(),s()}),function(t){return t?c(t,"ms")?M(t):1e3*M(t):0}(Qt(i,"transitionDuration")))})))).then((()=>delete t._reject))}function as(t){return Lt(document,"focusin",(e=>{H(ss)===t&&!t.$el.contains(e.target)&&t.$el.focus()}))}function ls(t){return Lt(document,Ze,(({target:e})=>{H(ss)!==t||t.overlay&&!t.$el.contains(e)||t.panel.contains(e)||qt(document,`${Qe} ${ii} scroll`,(({defaultPrevented:i,type:n,target:s})=>{!i&&n===Qe&&e===s&&t.hide()}),!0)}))}function hs(t){return Lt(document,"keydown",(e=>{27===e.keyCode&&H(ss)===t&&t.hide()}))}var cs={slide:{show:t=>[{transform:us(-100*t)},{transform:us()}],percent:t=>ds(t),translate:(t,e)=>[{transform:us(-100*e*t)},{transform:us(100*e*(1-t))}]}};function ds(t){return Math.abs(Qt(t,"transform").split(",")[4]/t.offsetWidth)}function us(t=0,e="%"){return`translate3d(${t+=t?e:""}, 0, 0)`}function fs(t){return`scale3d(${t}, ${t}, 1)`}function ps(t,e,i){Vt(t,Rt(e,!1,!1,i))}var ms={props:{i18n:Object},data:{i18n:null},methods:{t(t,...e){var i,n,s;let o=0;return(null==(s=(null==(i=this.i18n)?void 0:i[t])||(null==(n=this.$options.i18n)?void 0:n[t]))?void 0:s.replace(/%s/g,(()=>e[o++]||"")))||""}}},gs={props:{autoplay:Boolean,autoplayInterval:Number,pauseOnHover:Boolean},data:{autoplay:!1,autoplayInterval:7e3,pauseOnHover:!0},connected(){Q(this.list,"aria-live",this.autoplay?"off":"polite"),this.autoplay&&this.startAutoplay()},disconnected(){this.stopAutoplay()},update(){Q(this.slides,"tabindex","-1")},events:[{name:"visibilitychange",el:()=>document,filter(){return this.autoplay},handler(){document.hidden?this.stopAutoplay():this.startAutoplay()}}],methods:{startAutoplay(){this.stopAutoplay(),this.interval=setInterval((()=>{this.stack.length||this.draggable&&bt(this.$el,":focus-within")&&!bt(this.$el,":focus")||this.pauseOnHover&&bt(this.$el,":hover")||this.show("next")}),this.autoplayInterval)},stopAutoplay(){clearInterval(this.interval)}}};const vs={passive:!1,capture:!0},ws={passive:!0,capture:!0},bs="touchmove mousemove",xs="touchend touchcancel mouseup click input scroll",$s=t=>t.preventDefault();var ys={props:{draggable:Boolean},data:{draggable:!0,threshold:10},created(){for(const t of["start","move","end"]){const e=this[t];this[t]=t=>{const i=Zt(t).x*(Xe?-1:1);this.prevPos=i===this.pos?this.prevPos:this.pos,this.pos=i,e(t)}}},events:[{name:"touchstart mousedown",passive:!0,delegate(){return`${this.selList} > *`},handler(t){!this.draggable||this.parallax||!Gt(t)&&function(t){return"none"!==Qt(t,"userSelect")&&p(t.childNodes).some((t=>3===t.nodeType&&t.textContent.trim()))}(t.target)||t.target.closest(ft)||t.button>0||this.length<2||this.start(t)}},{name:"dragstart",handler(t){t.preventDefault()}},{name:bs,el(){return this.list},handler:R,...vs}],methods:{start(){this.drag=this.pos,this._transitioner?(this.percent=this._transitioner.percent(),this.drag+=this._transitioner.getDistance()*this.percent*this.dir,this._transitioner.cancel(),this._transitioner.translate(this.percent),this.dragging=!0,this.stack=[]):this.prevIndex=this.index,Lt(document,bs,this.move,vs),Lt(document,xs,this.end,ws),Qt(this.list,"userSelect","none")},move(t){const e=this.pos-this.drag;if(0===e||this.prevPos===this.pos||!this.dragging&&Math.abs(e)<this.threshold)return;this.dragging||Lt(this.list,"click",$s,vs),t.cancelable&&t.preventDefault(),this.dragging=!0,this.dir=e<0?1:-1;let{slides:i,prevIndex:n}=this,s=Math.abs(e),o=this.getIndex(n+this.dir),r=Ss.call(this,n,o);for(;o!==n&&s>r;)this.drag-=r*this.dir,n=o,s-=r,o=this.getIndex(n+this.dir),r=Ss.call(this,n,o);this.percent=s/r;const a=i[n],l=i[o],h=this.index!==o,c=n===o;let u;for(const t of[this.index,this.prevIndex])d([o,n],t)||(Vt(i[t],"itemhidden",[this]),c&&(u=!0,this.prevIndex=n));(this.index===n&&this.prevIndex!==n||u)&&Vt(i[this.index],"itemshown",[this]),h&&(this.prevIndex=n,this.index=o,c||(Vt(a,"beforeitemhide",[this]),Vt(a,"itemhide",[this])),Vt(l,"beforeitemshow",[this]),Vt(l,"itemshow",[this])),this._transitioner=this._translate(Math.abs(this.percent),a,!c&&l)},end(){if(Wt(document,bs,this.move,vs),Wt(document,xs,this.end,ws),this.dragging)if(this.dragging=null,this.index===this.prevIndex)this.percent=1-this.percent,this.dir*=-1,this._show(!1,this.index,!0),this._transitioner=null;else{const t=(Xe?this.dir*(Xe?1:-1):this.dir)<0==this.prevPos>this.pos;this.index=t?this.index:this.prevIndex,t&&(this.percent=1-this.percent),this.show(this.dir>0&&!t||this.dir<0&&t?"next":"previous",!0)}setTimeout((()=>Wt(this.list,"click",$s,vs))),Qt(this.list,{userSelect:""}),this.drag=this.percent=null}}};function Ss(t,e){return this._getTransitioner(t,t!==e&&e).getDistance()||this.slides[t].offsetWidth}function Is(t,e,i){t._watches.push({name:i,...w(e)?e:{handler:e}})}function ks(t,e,i){t._hasComputed=!0,Object.defineProperty(t,e,{enumerable:!0,get(){const{_computed:s,$props:o,$el:r}=t;return n(s,e)||(s[e]=(i.get||i).call(t,o,r)),s[e]},set(n){const{_computed:s}=t;s[e]=i.set?i.set.call(t,n):n,A(s[e])&&delete s[e]}})}function Cs(t){t._hasComputed&&(function(t,e){t._updates.unshift(e)}(t,{read:()=>function(t,e){for(const{name:i,handler:s,immediate:o=!0}of t._watches)(t._initial&&o||n(e,i)&&!N(e[i],t[i]))&&s.call(t,t[i],e[i]);t._initial=!1}(t,Ts(t)),events:["resize","computed"]}),Es||(As=new Set,Es=new MutationObserver((()=>{for(const t of As)fn(t,"computed")})),Es.observe(document,{subtree:!0,childList:!0})),As.add(t))}function Ts(t){const e={...t._computed};return t._computed={},e}let Es,As;function Ds(t,e,i){let{name:n,el:s,handler:o,capture:r,passive:a,delegate:l,filter:h,self:c}=w(e)?e:{name:i,handler:e};s=g(s)?s.call(t,t):s||t.$el,!(!s||f(s)&&!s.length||h&&!h.call(t))&&t._events.push(Lt(s,n,l?k(l)?l:l.call(t,t):null,k(o)?t[o]:o.bind(t),{passive:a,capture:r,self:c}))}function Ps(t,...e){t._observers.push(...e)}function Ms(t,e){let{observe:i,target:s=t.$el,handler:o,options:r,filter:a,args:l}=e;if(a&&!a.call(t,t))return;const h=`_observe${t._observers.length}`;g(s)&&!n(t,h)&&ks(t,h,(()=>s.call(t,t))),o=k(o)?t[o]:o.bind(t),g(r)&&(r=r.call(t,t));const c=i(n(t,h)?t[h]:s,o,r,l);g(s)&&f(t[h])&&c.unobserve&&Is(t,{handler:_s(c),immediate:!1},h),Ps(t,c)}function _s(t){return(e,i)=>{for(const n of i)!d(e,n)&&t.unobserve(n);for(const n of e)!d(i,n)&&t.observe(n)}}function Bs(t){const e={},{args:i=[],props:n={},el:s,id:r}=t;if(!n)return e;for(const t in n){const i=o(t);let r=it(s,i);A(r)||(r=n[t]===Boolean&&""===r||dn(n[t],r),("target"!==i||!h(r,"_"))&&(e[t]=r))}const l=cn(it(s,r),i);for(const t in l){const i=a(t);A(n[i])||(e[i]=dn(n[i],l[t]))}return e}const Os=K(((t,e)=>{const i=Object.keys(e),n=i.concat(t).map((t=>[o(t),`data-${o(t)}`])).flat();return{attributes:i,filter:n}}));function Ns(t,e){var i;null==(i=t.$options[e])||i.forEach((e=>e.call(t)))}function zs(t){t._connected||(function(t){const{$options:e,$props:i}=t,s=Bs(e);m(i,s);const{computed:o,methods:r}=e;for(let e in i)e in s&&(!o||!n(o,e))&&(!r||!n(r,e))&&(t[e]=i[e])}(t),Ns(t,"beforeConnect"),t._connected=!0,function(t){t._events=[];for(const e of t.$options.events||[])if(n(e,"handler"))Ds(t,e);else for(const i in e)Ds(t,e[i],i)}(t),function(t){t._data={},t._updates=[...t.$options.update||[]]}(t),function(t){t._watches=[];for(const e of t.$options.watch||[])for(const[i,n]of Object.entries(e))Is(t,n,i);t._initial=!0}(t),function(t){t._observers=[];for(const e of t.$options.observe||[])if(n(e,"handler"))Ms(t,e);else for(const i of e)Ms(t,i)}(t),function(t){const{$options:e,$props:i}=t,{id:n,props:s,el:o}=e;if(!s)return;const{attributes:r,filter:l}=Os(n,s),h=new MutationObserver((s=>{const o=Bs(e);s.some((({attributeName:t})=>{const e=t.replace("data-","");return(e===n?r:[a(e),a(t)]).some((t=>!A(o[t])&&o[t]!==i[t]))}))&&t.$reset()}));h.observe(o,{attributes:!0,attributeFilter:l}),Ps(t,h)}(t),Cs(t),Ns(t,"connected"),fn(t))}function Hs(t){t._connected&&(Ns(t,"beforeDisconnect"),function(t){t._events.forEach((t=>t())),delete t._events}(t),function(t){delete t._data}(t),function(t){for(const e of t._observers)e.disconnect()}(t),function(t){null==As||As.delete(t),Ts(t)}(t),Ns(t,"disconnected"),t._connected=!1)}let Fs=0;function js(t,e={}){e.data=function({data:t={}},{args:e=[],props:i={}}){f(t)&&(t=t.slice(0,e.length).reduce(((t,i,n)=>(w(i)?m(t,i):t[e[n]]=i,t)),{}));for(const e in t)A(t[e])?delete t[e]:i[e]&&(t[e]=dn(i[e],t[e]));return t}(e,t.constructor.options),t.$options=hn(t.constructor.options,e,t),t.$props={},t._uid=Fs++,function(t){const{data:e={}}=t.$options;for(const i in e)t.$props[i]=t[i]=e[i]}(t),function(t){const{methods:e}=t.$options;if(e)for(const i in e)t[i]=e[i].bind(t)}(t),function(t){const{computed:e}=t.$options;if(t._computed={},e)for(const i in e)ks(t,i,e[i])}(t),Ns(t,"created"),e.el&&t.$mount(e.el)}const Ls=function(t){js(this,t)};Ls.util=tn,Ls.options={},Ls.version="3.19.1";const Ws="uc-",qs="__uikit__",Vs={};function Rs(t,e){var i;const n=Ws+o(t);if(!e)return w(Vs[n])&&(Vs[n]=Ls.extend(Vs[n])),Vs[n];t=a(t),Ls[t]=(e,i)=>Us(t,e,i);const s=w(e)?{...e}:e.options;return s.id=n,s.name=t,null==(i=s.install)||i.call(s,Ls,s,t),Ls._initialized&&!s.functional&&requestAnimationFrame((()=>Us(t,`[${n}],[data-${n}]`))),Vs[n]=s}function Us(t,e,i,...n){const s=Rs(t);return s.options.functional?new s({data:w(e)?e:[e,i,...n]}):e?Ee(e).map(o)[0]:o();function o(e){const n=Xs(e,t);if(n){if(!i)return n;n.$destroy()}return new s({el:e,data:i})}}function Ys(t){return(null==t?void 0:t[qs])||{}}function Xs(t,e){return Ys(t)[e]}function Js(t,e){t=t?_(t):document.body;for(const i of yt(t).reverse())Gs(i,e);Ce(t,(t=>Gs(t,e)))}function Gs(t,e){const i=Ys(t);for(const t in i)fn(i[t],e)}let Zs=1;function Ks(t,e=null){return(null==e?void 0:e.id)||`${t.$options.id}-${Zs++}`}var Qs={i18n:{next:"Next slide",previous:"Previous slide",slideX:"Slide %s",slideLabel:"%s of %s",role:"String"},data:{selNav:!1,role:"region"},computed:{nav:({selNav:t},e)=>Te(t,e),navChildren(){return St(this.nav)},selNavItem:({attrItem:t})=>`[${t}],[data-${t}]`,navItems(t,e){return Ee(this.selNavItem,e)}},watch:{nav(t,e){Q(t,"role","tablist"),e&&this.$emit()},list(t){Q(t,"role","presentation")},navChildren(t){Q(t,"role","presentation")},navItems(t){for(const e of t){const t=it(e,this.attrItem),i=Te("a,button",e)||e;let n,s=null;if(T(t)){const e=P(t),o=this.slides[e];o&&(o.id||(o.id=Ks(this,o)),s=o.id),n=this.t("slideX",M(t)+1),Q(i,"role","tab")}else this.list&&(this.list.id||(this.list.id=Ks(this,this.list)),s=this.list.id),n=this.t(t);Q(i,{"aria-controls":s,"aria-label":Q(i,"aria-label")||n})}},slides(t){t.forEach(((t,e)=>Q(t,{role:this.nav?"tabpanel":"group","aria-label":this.t("slideLabel",e+1,this.length),"aria-roledescription":this.nav?null:"slide"})))},length(t){const e=this.navChildren.length;if(this.nav&&t!==e){ue(this.nav);for(let e=0;e<t;e++)me(this.nav,`<li ${this.attrItem}="${e}"><a href></a></li>`)}}},connected(){Q(this.$el,{role:this.role,"aria-roledescription":"carousel"})},update:[{write(){this.navItems.concat(this.nav).forEach((t=>t&&(t.hidden=!this.maxIndex))),this.updateNav()},events:["resize"]}],events:[{name:"click keydown",delegate(){return this.selNavItem},filter(){return!this.parallax},handler(t){t.target.closest("a,button")&&("click"===t.type||t.keyCode===Fn)&&(t.preventDefault(),this.show(it(t.current,this.attrItem)))}},{name:"itemshow",handler:"updateNav"},{name:"keydown",delegate(){return this.selNavItem},filter(){return!this.parallax},handler(t){const{current:e,keyCode:i}=t;if(!T(it(e,this.attrItem)))return;let n=i===Ln?0:i===jn?"last":i===Wn?"previous":i===Vn?"next":-1;~n&&(t.preventDefault(),this.show(n))}}],methods:{updateNav(){const t=this.getValidIndex();for(const e of this.navItems){const i=it(e,this.attrItem),n=Te("a,button",e)||e;if(T(i)){const s=P(i)===t;lt(e,this.clsActive,s),lt(n,"uc-disabled",this.parallax),Q(n,{"aria-selected":s,tabindex:s&&!this.parallax?null:-1}),s&&n&&bt(vt(e),":focus-within")&&n.focus()}else lt(e,"uc-invisible",this.finite&&("previous"===i&&0===t||"next"===i&&t>=this.maxIndex))}}}},to={mixins:[gs,ys,Qs,ms],props:{clsActivated:String,easing:String,index:Number,finite:Boolean,velocity:Number},data:()=>({easing:"ease",finite:!1,velocity:1,index:0,prevIndex:-1,stack:[],percent:0,clsActive:"uc-active",clsActivated:"",clsEnter:"uc-slide-enter",clsLeave:"uc-slide-leave",clsSlideActive:"uc-slide-active",Transitioner:!1,transitionOptions:{}}),connected(){this.prevIndex=-1,this.index=this.getValidIndex(this.$props.index),this.stack=[]},disconnected(){st(this.slides,this.clsActive)},computed:{duration:({velocity:t},e)=>eo(e.offsetWidth/t),list:({selList:t},e)=>Te(t,e),maxIndex(){return this.length-1},slides(){return St(this.list)},length(){return this.slides.length}},watch:{slides(t,e){e&&this.$emit()}},observe:pn(),events:{itemshow({target:t}){nt(t,this.clsEnter,this.clsSlideActive)},itemshown({target:t}){st(t,this.clsEnter)},itemhide({target:t}){nt(t,this.clsLeave)},itemhidden({target:t}){st(t,this.clsLeave,this.clsSlideActive)}},methods:{show(t,e=!1){var i;if(this.dragging||!this.length||this.parallax)return;const{stack:n}=this,s=e?0:n.length,o=()=>{n.splice(s,1),n.length&&this.show(n.shift(),!0)};if(n[e?"unshift":"push"](t),!e&&n.length>1)return void(2===n.length&&(null==(i=this._transitioner)||i.forward(Math.min(this.duration,200))));const r=this.getIndex(this.index),a=at(this.slides,this.clsActive)&&this.slides[r],l=this.getIndex(t,this.index),h=this.slides[l];if(a===h)return void o();if(this.dir=function(t,e){return"next"===t?1:"previous"===t||t<e?-1:1}(t,r),this.prevIndex=r,this.index=l,a&&!Vt(a,"beforeitemhide",[this])||!Vt(h,"beforeitemshow",[this,a]))return this.index=this.prevIndex,void o();const c=this._show(a,h,e).then((()=>{a&&Vt(a,"itemhidden",[this]),Vt(h,"itemshown",[this]),n.shift(),this._transitioner=null,requestAnimationFrame((()=>n.length&&this.show(n.shift(),!0)))}));return a&&Vt(a,"itemhide",[this]),Vt(h,"itemshow",[this]),c},getIndex(t=this.index,e=this.index){return V(Z(t,this.slides,e,this.finite),0,Math.max(0,this.maxIndex))},getValidIndex(t=this.index,e=this.prevIndex){return this.getIndex(t,e)},_show(t,e,i){if(this._transitioner=this._getTransitioner(t,e,this.dir,{easing:i?e.offsetWidth<600?"cubic-bezier(0.25, 0.46, 0.45, 0.94)":"cubic-bezier(0.165, 0.84, 0.44, 1)":this.easing,...this.transitionOptions}),!i&&!t)return this._translate(1),Promise.resolve();const{length:n}=this.stack;return this._transitioner[n>1?"forward":"show"](n>1?Math.min(this.duration,75+75/(n-1)):this.duration,this.percent)},_translate(t,e=this.prevIndex,i=this.index){const n=this._getTransitioner(e!==i&&e,i);return n.translate(t),n},_getTransitioner(t=this.prevIndex,e=this.index,i=this.dir||1,n=this.transitionOptions){return new this.Transitioner(C(t)?this.slides[t]:t,C(e)?this.slides[e]:e,i*(Xe?-1:1),n)}}};function eo(t){return.5*t+300}var io={mixins:[to],props:{animation:String},data:{animation:"slide",clsActivated:"uc-transition-active",Animations:cs,Transitioner:function(t,e,i,{animation:n,easing:s}){const{percent:o,translate:r,show:a=R}=n,l=a(i);let h;return{dir:i,show(n,o=0,r){const a=r?"linear":s;return n-=Math.round(n*V(o,-1,1)),this.translate(o),ps(e,"itemin",{percent:o,duration:n,timing:a,dir:i}),ps(t,"itemout",{percent:1-o,duration:n,timing:a,dir:i}),new Promise((i=>{h||(h=i),Promise.all([se.start(e,l[1],n,a),se.start(t,l[0],n,a)]).then((()=>{this.reset(),h()}),R)}))},cancel:()=>se.cancel([e,t]),reset(){for(const i in l[0])Qt([e,t],i,"")},async forward(t,e=this.percent()){return await this.cancel(),this.show(t,e,!0)},translate(n){this.reset();const s=r(n,i);Qt(e,s[1]),Qt(t,s[0]),ps(e,"itemtranslatein",{percent:n,dir:i}),ps(t,"itemtranslateout",{percent:1-n,dir:i})},percent:()=>o(t||e,e,i),getDistance:()=>null==t?void 0:t.offsetWidth}}},computed:{animation:({animation:t,Animations:e})=>({...e[t]||e.slide,name:t}),transitionOptions(){return{animation:this.animation}}},events:{beforeitemshow({target:t}){nt(t,this.clsActive)},itemshown({target:t}){nt(t,this.clsActivated)},itemhidden({target:t}){st(t,this.clsActive,this.clsActivated)}}},no={...cs,fade:{show:()=>[{opacity:0},{opacity:1}],percent:t=>1-Qt(t,"opacity"),translate:t=>[{opacity:1-t},{opacity:t}]},scale:{show:()=>[{opacity:0,transform:fs(.8)},{opacity:1,transform:fs(1)}],percent:t=>1-Qt(t,"opacity"),translate:t=>[{opacity:1-t,transform:fs(1-.2*t)},{opacity:t,transform:fs(.8+.2*t)}]}},so={mixins:[os,io],functional:!0,props:{delayControls:Number,preload:Number,videoAutoplay:Boolean,template:String},data:()=>({preload:1,videoAutoplay:!1,delayControls:3e3,items:[],cls:"uc-open",clsPage:"uc-lightbox-page",selList:".uc-lightbox-items",attrItem:"uc-lightbox-item",selClose:".uc-close-large",selCaption:".uc-lightbox-caption",pauseOnHover:!1,velocity:2,Animations:no,template:'<div class="uc-lightbox uc-overflow-hidden"> <ul class="uc-lightbox-items"></ul> <div class="uc-lightbox-toolbar uc-position-top uc-text-right uc-transition-slide-top uc-transition-opaque"> <button class="uc-lightbox-toolbar-icon uc-close-large" type="button" uc-close></button> </div> <a class="uc-lightbox-button uc-position-center-left uc-position-medium uc-transition-fade" href uc-slidenav-previous uc-lightbox-item="previous"></a> <a class="uc-lightbox-button uc-position-center-right uc-position-medium uc-transition-fade" href uc-slidenav-next uc-lightbox-item="next"></a> <div class="uc-lightbox-toolbar uc-lightbox-caption uc-position-bottom uc-text-center uc-transition-slide-bottom uc-transition-opaque"></div> </div>'}),created(){const t=Te(this.template),e=Te(this.selList,t);this.items.forEach((()=>me(e,"<li>")));const i=Te("[uc-close]",t),n=this.t("close");i&&n&&(i.dataset.i18n=JSON.stringify({label:n})),this.$mount(me(this.container,t))},computed:{caption:({selCaption:t},e)=>Te(t,e)},events:[{name:`${Ke} ${Ze} keydown`,handler:"showControls"},{name:"click",self:!0,delegate(){return`${this.selList} > *`},handler(t){t.defaultPrevented||this.hide()}},{name:"shown",self:!0,handler:"showControls"},{name:"hide",self:!0,handler(){this.hideControls(),st(this.slides,this.clsActive),se.stop(this.slides)}},{name:"hidden",self:!0,handler(){this.$destroy(!0)}},{name:"keyup",el:()=>document,handler({keyCode:t}){if(!this.isToggled(this.$el)||!this.draggable)return;let e=-1;t===Wn?e="previous":t===Vn?e="next":t===Ln?e=0:t===jn&&(e="last"),~e&&this.show(e)}},{name:"beforeitemshow",handler(t){this.isToggled()||(this.draggable=!1,t.preventDefault(),this.toggleElement(this.$el,!0,!1),this.animation=no.scale,st(t.target,this.clsActive),this.stack.splice(1,0,this.index))}},{name:"itemshow",handler(){fe(this.caption,this.getItem().caption||"");for(let t=-this.preload;t<=this.preload;t++)this.loadItem(this.index+t)}},{name:"itemshown",handler(){this.draggable=this.$props.draggable}},{name:"itemload",async handler(t,e){const{source:i,type:n,alt:s="",poster:o,attrs:r={}}=e;if(this.setItem(e,"<span uc-spinner></span>"),!i)return;let a;const l={allowfullscreen:"",style:"max-width: 100%; box-sizing: border-box;","uc-responsive":"","uc-video":`${this.videoAutoplay}`};if("image"===n||i.match(/\.(avif|jpe?g|jfif|a?png|gif|svg|webp)($|\?)/i)){const t=oo("img",{src:i,alt:s,...r});Lt(t,"load",(()=>this.setItem(e,t))),Lt(t,"error",(()=>this.setError(e)))}else if("video"===n||i.match(/\.(mp4|webm|ogv)($|\?)/i)){const t=oo("video",{src:i,poster:o,controls:"",playsinline:"","uc-video":`${this.videoAutoplay}`,...r});Lt(t,"loadedmetadata",(()=>this.setItem(e,t))),Lt(t,"error",(()=>this.setError(e)))}else if("iframe"===n||i.match(/\.(html|php)($|\?)/i))this.setItem(e,oo("iframe",{src:i,allowfullscreen:"",class:"uc-lightbox-iframe",...r}));else if(a=i.match(/\/\/(?:.*?youtube(-nocookie)?\..*?(?:[?&]v=|\/shorts\/)|youtu\.be\/)([\w-]{11})[&?]?(.*)?/))this.setItem(e,oo("iframe",{src:`https://www.youtube${a[1]||""}.com/embed/${a[2]}${a[3]?`?${a[3]}`:""}`,width:1920,height:1080,...l,...r}));else if(a=i.match(/\/\/.*?vimeo\.[a-z]+\/(\d+)[&?]?(.*)?/))try{const{height:t,width:n}=await(await fetch(`https://vimeo.com/api/oembed.json?maxwidth=1920&url=${encodeURI(i)}`,{credentials:"omit"})).json();this.setItem(e,oo("iframe",{src:`https://player.vimeo.com/video/${a[1]}${a[2]?`?${a[2]}`:""}`,width:n,height:t,...l,...r}))}catch{this.setError(e)}}}],methods:{loadItem(t=this.index){const e=this.getItem(t);this.getSlide(e).childElementCount||Vt(this.$el,"itemload",[e])},getItem(t=this.index){return this.items[Z(t,this.slides)]},setItem(t,e){Vt(this.$el,"itemloaded",[this,fe(this.getSlide(t),e)])},getSlide(t){return this.slides[this.items.indexOf(t)]},setError(t){this.setItem(t,'<span uc-icon="icon: bolt; ratio: 2"></span>')},showControls(){clearTimeout(this.controlsTimer),this.controlsTimer=setTimeout(this.hideControls,this.delayControls),nt(this.$el,"uc-active","uc-transition-active")},hideControls(){st(this.$el,"uc-active","uc-transition-active")}}};function oo(t,e){const i=Ie(`<${t}>`);return Q(i,e),i}var ro={install:function(t,e){t.lightboxPanel||t.component("lightboxPanel",so),m(e.props,t.component("lightboxPanel").options.props)},props:{toggle:String},data:{toggle:"a"},computed:{toggles:({toggle:t},e)=>Ee(t,e)},watch:{toggles(t){this.hide();for(const e of t)de(e,"a")&&Q(e,"role","button")}},disconnected(){this.hide()},events:{name:"click",delegate(){return`${this.toggle}:not(.uc-disabled)`},handler(t){t.preventDefault(),this.show(t.current)}},methods:{show(t){const e=W(this.toggles.map(ao),"source");if(y(t)){const{source:i}=ao(t);t=u(e,(({source:t})=>i===t))}return this.panel=this.panel||this.$create("lightboxPanel",{...this.$props,items:e}),Lt(this.panel.$el,"hidden",(()=>this.panel=null)),this.panel.show(t)},hide(){var t;return null==(t=this.panel)?void 0:t.hide()}}};function ao(t){const e={};for(const i of["href","caption","type","poster","alt","attrs"])e["href"===i?"source":i]=it(t,i);return e.attrs=cn(e.attrs),e}var lo={mixins:[Qn],functional:!0,args:["message","status"],data:{message:"",status:"",timeout:5e3,group:"",pos:"top-center",clsContainer:"uc-notification",clsClose:"uc-notification-close",clsMsg:"uc-notification-message"},install:function(t){t.notification.closeAll=function(e,i){Ce(document.body,(n=>{const s=t.getComponent(n,"notification");s&&(!e||e===s.group)&&s.close(i)}))}},computed:{marginProp:({pos:t})=>`margin-${t.match(/[a-z]+(?=-)/)[0]}`,startProps(){return{opacity:0,[this.marginProp]:-this.$el.offsetHeight}}},created(){const t=`${this.clsContainer}-${this.pos}`,e=`data-${this.clsContainer}-container`,i=Te(`.${t}[${e}]`,this.container)||me(this.container,`<div class="${this.clsContainer} ${t}" ${e}></div>`);this.$mount(me(i,`<div class="${this.clsMsg}${this.status?` ${this.clsMsg}-${this.status}`:""}" role="alert"> <a href class="${this.clsClose}" data-uc-close></a> <div>${this.message}</div> </div>`))},async connected(){const t=M(Qt(this.$el,this.marginProp));await se.start(Qt(this.$el,this.startProps),{opacity:1,[this.marginProp]:t}),this.timeout&&(this.timer=setTimeout(this.close,this.timeout))},events:{click(t){t.target.closest('a[href="#"],a[href=""]')&&t.preventDefault(),this.close()},[ti](){this.timer&&clearTimeout(this.timer)},[ei](){this.timeout&&(this.timer=setTimeout(this.close,this.timeout))}},methods:{async close(t){this.timer&&clearTimeout(this.timer),t||await se.start(this.$el,this.startProps),(t=>{const e=vt(t);Vt(t,"close",[this]),be(t),null!=e&&e.hasChildNodes()||be(e)})(this.$el)}}};var ho={props:{media:Boolean,mediaMax:Boolean},data:{media:!1,mediaMax:!1},connected(){const t=co(this.media,this.$el),e=co(this.mediaMax,this.$el,"max");if(this.matchMedia=!0,t||e){const i=t?e?t+" and "+e:t:e;this.mediaObj=window.matchMedia(i);const n=()=>{this.matchMedia=this.mediaObj.matches,Vt(this.$el,Rt("mediachange",!1,!0,[this.mediaObj]))};this.offMediaObj=Lt(this.mediaObj,"change",(()=>{n(),this.$emit("resize")})),n()}},disconnected(){var t;null==(t=this.offMediaObj)||t.call(this)}};function co(t,e,i="min"){if(k(t))if(h(t,"@"))t=M(Qt(e,`--uc-breakpoint-${t.substr(1)}`));else if(isNaN(t))return t;return t&&T(t)?`(${i}-width: ${t}px)`:""}function uo(t){return Math.ceil(Math.max(0,...Ee("[stroke]",t).map((t=>{try{return t.getTotalLength()}catch{return 0}}))))}const fo={x:go,y:go,rotate:go,scale:go,color:vo,backgroundColor:vo,borderColor:vo,blur:wo,hue:wo,fopacity:wo,grayscale:wo,invert:wo,saturate:wo,sepia:wo,opacity:function(t,e,i){return 1===i.length&&i.unshift(Ao(e,t,"")),i=Io(i),(e,n)=>{e[t]=Co(i,n)}},stroke:function(t,e,i){1===i.length&&i.unshift(0);const n=Eo(i),s=uo(e);return(i=Io(i.reverse(),(t=>(t=M(t),"%"===n?t*s/100:t)))).some((([t])=>t))?(Qt(e,"strokeDasharray",s),(t,e)=>{t.strokeDashoffset=Co(i,e)}):R},bgx:bo,bgy:bo},{keys:po}=Object;var mo={mixins:[ho],props:Do(po(fo),"list"),data:Do(po(fo),void 0),computed:{props(t,e){const i={};for(const e in t)e in fo&&!A(t[e])&&(i[e]=t[e].slice());const n={};for(const t in i)n[t]=fo[t](t,e,i[t],i);return n}},events:{load(){this.$emit()}},methods:{reset(){for(const t in this.getCss(0))Qt(this.$el,t,"")},getCss(t){const e={};for(const i in this.props)this.props[i](e,V(t));return e.willChange=Object.keys(e).map(te).join(","),e}}};function go(t,e,i){let n,s=Eo(i)||{x:"px",y:"px",rotate:"deg"}[t]||"";return"x"===t||"y"===t?(t=`translate${l(t)}`,n=t=>M(M(t).toFixed("px"===s?0:6))):"scale"===t&&(s="",n=t=>{var i;return Eo([t])?je(t,"width",e,!0)/e["offset"+(null!=(i=t.endsWith)&&i.call(t,"vh")?"Height":"Width")]:M(t)}),1===i.length&&i.unshift("scale"===t?1:0),i=Io(i,n),(e,n)=>{e.transform=`${e.transform||""} ${t}(${Co(i,n)}${s})`}}function vo(t,e,i){return 1===i.length&&i.unshift(Ao(e,t,"")),i=Io(i,(t=>function(t,e){return Ao(t,"color",e).split(/[(),]/g).slice(1,-1).concat(1).slice(0,4).map(M)}(e,t))),(e,n)=>{const[s,o,r]=ko(i,n),a=s.map(((t,e)=>(t+=r*(o[e]-t),3===e?M(t):parseInt(t,10)))).join(",");e[t]=`rgba(${a})`}}function wo(t,e,i){1===i.length&&i.unshift(0);const n=Eo(i)||{blur:"px",hue:"deg"}[t]||"%";return t={fopacity:"opacity",hue:"hue-rotate"}[t]||t,i=Io(i),(e,s)=>{const o=Co(i,s);e.filter=`${e.filter||""} ${t}(${o+n})`}}function bo(t,e,i,n){1===i.length&&i.unshift(0);const s="bgy"===t?"height":"width";n[t]=Io(i,(t=>je(t,s,e)));const o=["bgx","bgy"].filter((t=>t in n));if(2===o.length&&"bgx"===t)return R;if("cover"===Ao(e,"backgroundSize",""))return function(t,e,i,n){const s=function(t){const e=Qt(t,"backgroundImage").replace(/^none|url\(["']?(.+?)["']?\)$/,"$1");if(yo[e])return yo[e];const i=new Image;return e&&(i.src=e,!i.naturalWidth)?(i.onload=()=>{yo[e]=So(i),Vt(t,Rt("load",!1))},So(i)):yo[e]=So(i)}(e);if(!s.width)return R;const o={width:e.offsetWidth,height:e.offsetHeight},r=["bgx","bgy"].filter((t=>t in n)),a={};for(const t of r){const e=n[t].map((([t])=>t)),i=Math.min(...e),s=Math.max(...e),r=e.indexOf(i)<e.indexOf(s),l=s-i;a[t]=(r?-l:0)-(r?i:s)+"px",o["bgy"===t?"height":"width"]+=l}const l=G.cover(s,o);for(const t of r){const i="bgy"===t?"height":"width",n=l[i]-o[i];a[t]=`max(${xo(e,t)},-${n}px) + ${a[t]}`}const h=$o(r,a,n);return(t,e)=>{h(t,e),t.backgroundSize=`${l.width}px ${l.height}px`,t.backgroundRepeat="no-repeat"}}(0,e,0,n);const r={};for(const t of o)r[t]=xo(e,t);return $o(o,r,n)}function xo(t,e){return Ao(t,`background-position-${e.substr(-1)}`,"")}function $o(t,e,i){return function(n,s){for(const o of t){const t=Co(i[o],s);n[`background-position-${o.substr(-1)}`]=`calc(${e[o]} + ${t}px)`}}}const yo={};function So(t){return{width:t.naturalWidth,height:t.naturalHeight}}function Io(t,e=M){const i=[],{length:n}=t;let s=0;for(let o=0;o<n;o++){let[r,a]=k(t[o])?t[o].trim().split(/ (?![^(]*\))/):[t[o]];if(r=e(r),a=a?M(a)/100:null,0===o?null===a?a=0:a&&i.push([r,0]):o===n-1&&(null===a?a=1:1!==a&&(i.push([r,a]),a=1)),i.push([r,a]),null===a)s++;else if(s){const t=i[o-s-1][1],e=(a-t)/(s+1);for(let n=s;n>0;n--)i[o-n][1]=t+e*(s-n+1);s=0}}return i}function ko(t,e){const i=u(t.slice(1),(([,t])=>e<=t))+1;return[t[i-1][0],t[i][0],(e-t[i-1][1])/(t[i][1]-t[i-1][1])]}function Co(t,e){const[i,n,s]=ko(t,e);return i+Math.abs(i-n)*s*(i<n?1:-1)}const To=/^-?\d+(?:\.\d+)?(\S+)?/;function Eo(t,e){var i;for(const e of t){const t=null==(i=e.match)?void 0:i.call(e,To);if(t)return t[1]}return e}function Ao(t,e,i){const n=t.style[e],s=Qt(Qt(t,e,i),e);return t.style[e]=n,s}function Do(t,e){return t.reduce(((t,i)=>(t[i]=e,t)),{})}function Po(t,e){return e>=0?Math.pow(t,e+1):1-Math.pow(1-t,1-e)}var Mo={mixins:[mo],props:{target:String,viewport:Number,easing:Number,start:String,end:String},data:{target:!1,viewport:1,easing:1,start:0,end:0},computed:{target:({target:t},e)=>_o(t&&Tt(t,e)||e),start({start:t}){return je(t,"height",this.target,!0)},end({end:t,viewport:e}){return je(t||(e=100*(1-e))&&`${e}vh+${e}%`,"height",this.target,!0)}},observe:[wn(),bn({target:({target:t})=>t}),pn({target:({$el:t,target:e})=>[t,e,Pi(e,!0)]})],update:{read({percent:t},e){if(e.has("scroll")||(t=!1),!ut(this.$el))return!1;if(!this.matchMedia)return;const i=t;return{percent:t=Po(Ai(this.target,this.start,this.end),this.easing),style:i!==t&&this.getCss(t)}},write({style:t}){this.matchMedia?t&&Qt(this.$el,t):this.reset()},events:["scroll","resize"]}};function _o(t){return t?"offsetTop"in t?t:_o(vt(t)):document.documentElement}var Bo={props:{parallax:Boolean,parallaxTarget:Boolean,parallaxStart:String,parallaxEnd:String,parallaxEasing:Number},data:{parallax:!1,parallaxTarget:!1,parallaxStart:0,parallaxEnd:0,parallaxEasing:0},observe:[pn({target:({$el:t,parallaxTarget:e})=>[t,e],filter:({parallax:t})=>t}),bn({filter:({parallax:t})=>t})],computed:{parallaxTarget({parallaxTarget:t},e){return t&&Tt(t,e)||this.list}},update:{write(){if(!this.parallax)return;const t=this.parallaxTarget,e=Po(Ai(t,je(this.parallaxStart,"height",t,!0),je(this.parallaxEnd,"height",t,!0)),this.parallaxEasing),[i,n]=this.getIndexAt(e),s=this.getValidIndex(i+Math.ceil(n)),o=this.slides[i],r=this.slides[s],{triggerShow:a,triggerShown:l,triggerHide:h,triggerHidden:c}=function(t){const{clsSlideActive:e,clsEnter:i,clsLeave:n}=t;return{triggerShow:s,triggerShown:o,triggerHide:r,triggerHidden:a};function s(i){at(i,n)&&(r(i),a(i)),at(i,e)||(Vt(i,"beforeitemshow",[t]),Vt(i,"itemshow",[t]))}function o(e){at(e,i)&&Vt(e,"itemshown",[t])}function r(r){at(r,e)||s(r),at(r,i)&&o(r),at(r,n)||(Vt(r,"beforeitemhide",[t]),Vt(r,"itemhide",[t]))}function a(e){at(e,n)&&Vt(e,"itemhidden",[t])}}(this);if(~this.prevIndex)for(const t of new Set([this.index,this.prevIndex]))d([s,i],t)||(h(this.slides[t]),c(this.slides[t]));const u=this.prevIndex!==i||this.index!==s;this.dir=1,this.prevIndex=i,this.index=s,o!==r&&h(o),a(r),u&&l(o),this._translate(o===r?1:n,o,r)},events:["scroll","resize"]},methods:{getIndexAt(t){const e=t*(this.length-1);return[Math.floor(e),e%1]}}};var Oo={update:{write(){if(this.stack.length||this.dragging||this.parallax)return;const t=this.getValidIndex();~this.prevIndex&&this.index===t?this._translate(1,this.prevIndex,this.index):this.show(t)},events:["resize"]}},No={observe:vn({target:({slides:t})=>t,targets:t=>t.getAdjacentSlides()}),methods:{getAdjacentSlides(){return[1,-1].map((t=>this.slides[this.getIndex(this.index+t)]))}}};function zo(t,e,i){const n=jo(t,e);return i?n-function(t,e){return Pe(e).width/2-Pe(t).width/2}(t,e):Math.min(n,Ho(e))}function Ho(t){return Math.max(0,Fo(t)-Pe(t).width)}function Fo(t,e){return L(St(t).slice(0,e),(t=>Pe(t).width))}function jo(t,e){return t&&(_e(t).left+(Xe?Pe(t).width-Pe(e).width:0))*(Xe?-1:1)||0}function Lo(t,e){e-=1;const i=Pe(t).width,n=e+i+2;return St(t).filter((s=>{const o=jo(s,t),r=o+Math.min(Pe(s).width,i);return o>=e&&r<=n}))}function Wo(t,e,i){Vt(t,Rt(e,!1,!1,i))}var qo={mixins:[en,to,Oo,Bo,No],props:{center:Boolean,sets:Boolean,active:String},data:{center:!1,sets:!1,attrItem:"uc-slider-item",selList:".uc-slider-items",selNav:".uc-slider-nav",clsContainer:"uc-slider-container",active:"all",Transitioner:function(t,e,i,{center:n,easing:s,list:o}){const r=t?zo(t,o,n):zo(e,o,n)+Pe(e).width*i,a=e?zo(e,o,n):r+Pe(t).width*i*(Xe?-1:1);let l;return{dir:i,show(e,n=0,r){const h=r?"linear":s;return e-=Math.round(e*V(n,-1,1)),this.translate(n),n=t?n:V(n,0,1),Wo(this.getItemIn(),"itemin",{percent:n,duration:e,timing:h,dir:i}),t&&Wo(this.getItemIn(!0),"itemout",{percent:1-n,duration:e,timing:h,dir:i}),new Promise((t=>{l||(l=t),se.start(o,{transform:us(-a*(Xe?-1:1),"px")},e,h).then(l,R)}))},cancel:()=>se.cancel(o),reset(){Qt(o,"transform","")},async forward(t,e=this.percent()){return await this.cancel(),this.show(t,e,!0)},translate(n){const s=this.getDistance()*i*(Xe?-1:1);Qt(o,"transform",us(V(s-s*n-a,-Fo(o),Pe(o).width)*(Xe?-1:1),"px"));const r=this.getActives(),l=this.getItemIn(),h=this.getItemIn(!0);n=t?V(n,-1,1):0;for(const s of St(o)){const a=d(r,s),c=s===l,u=s===h;Wo(s,"itemtranslate"+(c||!u&&(a||i*(Xe?-1:1)==-1^jo(s,o)>jo(t||e))?"in":"out"),{dir:i,percent:u?1-n:c?n:a?1:0})}},percent:()=>Math.abs((Qt(o,"transform").split(",")[4]*(Xe?-1:1)+r)/(a-r)),getDistance:()=>Math.abs(a-r),getItemIn(i=!1){let s=this.getActives(),r=Lo(o,zo(e||t,o,n));if(i){const t=s;s=r,r=t}return r[u(r,(t=>!d(s,t)))]},getActives:()=>Lo(o,zo(t||e,o,n))}}},computed:{finite({finite:t}){return t||function(t,e){if(!t||t.length<2)return!0;const{width:i}=Pe(t);if(!e)return Math.ceil(Fo(t))<Math.trunc(i+function(t){return Math.max(0,...St(t).map((t=>Pe(t).width)))}(t));const n=St(t),s=Math.trunc(i/2);for(const t in n){const e=n[t],i=Pe(e).width,o=new Set([e]);let r=0;for(const e of[-1,1]){let a=i/2,l=0;for(;a<s;){const i=n[Z(+t+e+l++*e,n)];if(o.has(i))return!0;a+=Pe(i).width,o.add(i)}r=Math.max(r,i/2+Pe(n[Z(+t+e,n)]).width/2-(a-s))}if(Math.trunc(r)>L(n.filter((t=>!o.has(t))),(t=>Pe(t).width)))return!0}return!1}(this.list,this.center)},maxIndex(){if(!this.finite||this.center&&!this.sets)return this.length-1;if(this.center)return H(this.sets);let t=0;const e=Ho(this.list),i=u(this.slides,(i=>{if(t>=e)return!0;t+=Pe(i).width}));return~i?i:this.length-1},sets({sets:t}){if(!t||this.parallax)return;let e=0;const i=[],n=Pe(this.list).width;for(let t=0;t<this.length;t++){const s=Pe(this.slides[t]).width;e+s>n&&(e=0),this.center?e<n/2&&e+s+Pe(this.slides[Z(+t+1,this.slides)]).width/2>n/2&&(i.push(+t),e=n/2-s/2):0===e&&i.push(Math.min(+t,this.maxIndex)),e+=s}return i.length?i:void 0},transitionOptions(){return{center:this.center,list:this.list}},slides(){return St(this.list).filter(ut)}},connected(){lt(this.$el,this.clsContainer,!Te(`.${this.clsContainer}`,this.$el))},observe:pn({target:({slides:t})=>t}),update:{write(){for(const t of this.navItems){const e=P(it(t,this.attrItem));!1!==e&&(t.hidden=!this.maxIndex||e>this.maxIndex||this.sets&&!d(this.sets,e))}this.reorder(),this.updateActiveClasses()},events:["resize"]},events:{beforeitemshow(t){!this.dragging&&this.sets&&this.stack.length<2&&!d(this.sets,this.index)&&(this.index=this.getValidIndex());const e=Math.abs(this.index-this.prevIndex+(this.dir>0&&this.index<this.prevIndex||this.dir<0&&this.index>this.prevIndex?(this.maxIndex+1)*this.dir:0));if(!this.dragging&&e>1){for(let t=0;t<e;t++)this.stack.splice(1,0,this.dir>0?"next":"previous");return void t.preventDefault()}const i=this.dir<0||!this.slides[this.prevIndex]?this.index:this.prevIndex,n=Fo(this.list)/this.length;this.duration=eo(n/this.velocity)*(Pe(this.slides[i]).width/n),this.reorder()},itemshow(){~this.prevIndex&&nt(this._getTransitioner().getItemIn(),this.clsActive),this.updateActiveClasses(this.prevIndex)},itemshown(){this.updateActiveClasses()}},methods:{reorder(){if(this.finite)return void Qt(this.slides,"order","");const t=this.dir>0&&this.slides[this.prevIndex]?this.prevIndex:this.index;if(this.slides.forEach(((e,i)=>Qt(e,"order",this.dir>0&&i<t?1:this.dir<0&&i>=this.index?-1:""))),!this.center)return;const e=this.slides[t];let i=Pe(this.list).width/2-Pe(e).width/2,n=0;for(;i>0;){const e=this.getIndex(--n+t,t),s=this.slides[e];Qt(s,"order",e>t?-2:-1),i-=Pe(s).width}},updateActiveClasses(t=this.index){let e=this._getTransitioner(t).getActives();"all"!==this.active&&(e=[this.slides[this.getValidIndex(t)]]);const i=[this.clsActive,!this.sets||d(this.sets,M(this.index))?this.clsActivated:""];for(const t of this.slides){const s=d(e,t);lt(t,i,s),Q(t,"aria-hidden",!s);for(const e of Ee(mt,t))n(e,"_tabindex")||(e._tabindex=Q(e,"tabindex")),Q(e,"tabindex",s?e._tabindex:-1)}},getValidIndex(t=this.index,e=this.prevIndex){if(t=this.getIndex(t,e),!this.sets)return t;let i;do{if(d(this.sets,t))return t;i=t,t=this.getIndex(t+this.dir,e)}while(t!==i);return t},getAdjacentSlides(){const{width:t}=Pe(this.list),e=-t,i=2*t,n=Pe(this.slides[this.index]).width,s=this.center?t/2-n/2:0,o=new Set;for(const t of[-1,1]){let r=s+(t>0?n:0),a=0;do{const e=this.slides[this.getIndex(this.index+t+a++*t)];r+=Pe(e).width*t,o.add(e)}while(this.length>a&&r>e&&r<i)}return Array.from(o)},getIndexAt(t){let e=-1;let i=t*(this.center?Fo(this.list)-(Pe(this.slides[0]).width/2+Pe(H(this.slides)).width/2):Fo(this.list,this.maxIndex)),n=0;do{const t=Pe(this.slides[++e]).width,s=this.center?t/2+Pe(this.slides[e+1]).width/2:t;n=i/s%1,i-=s}while(i>=0&&e<this.maxIndex);return[e,n]}}};var Vo={mixins:[mo],data:{selItem:"!li"},beforeConnect(){this.item=Tt(this.selItem,this.$el)},disconnected(){this.item=null},events:[{name:"itemin itemout",self:!0,el(){return this.item},handler({type:t,detail:{percent:e,duration:i,timing:n,dir:s}}){ni.read((()=>{if(!this.matchMedia)return;const o=this.getCss(Uo(t,s,e)),r=this.getCss(Ro(t)?.5:s>0?1:0);ni.write((()=>{Qt(this.$el,o),se.start(this.$el,r,i,n).catch(R)}))}))}},{name:"transitioncanceled transitionend",self:!0,el(){return this.item},handler(){se.cancel(this.$el)}},{name:"itemtranslatein itemtranslateout",self:!0,el(){return this.item},handler({type:t,detail:{percent:e,dir:i}}){ni.read((()=>{if(!this.matchMedia)return void this.reset();const n=this.getCss(Uo(t,i,e));ni.write((()=>Qt(this.$el,n)))}))}}]};function Ro(t){return c(t,"in")}function Uo(t,e,i){return i/=2,Ro(t)^e<0?i:1-i}var Yo={...cs,fade:{show:()=>[{opacity:0,zIndex:0},{zIndex:-1}],percent:t=>1-Qt(t,"opacity"),translate:t=>[{opacity:1-t,zIndex:0},{zIndex:-1}]},scale:{show:()=>[{opacity:0,transform:fs(1.5),zIndex:0},{zIndex:-1}],percent:t=>1-Qt(t,"opacity"),translate:t=>[{opacity:1-t,transform:fs(1+.5*t),zIndex:0},{zIndex:-1}]},pull:{show:t=>t<0?[{transform:us(30),zIndex:-1},{transform:us(),zIndex:0}]:[{transform:us(-100),zIndex:0},{transform:us(),zIndex:-1}],percent:(t,e,i)=>i<0?1-ds(e):ds(t),translate:(t,e)=>e<0?[{transform:us(30*t),zIndex:-1},{transform:us(-100*(1-t)),zIndex:0}]:[{transform:us(100*-t),zIndex:0},{transform:us(30*(1-t)),zIndex:-1}]},push:{show:t=>t<0?[{transform:us(100),zIndex:0},{transform:us(),zIndex:-1}]:[{transform:us(-30),zIndex:-1},{transform:us(),zIndex:0}],percent:(t,e,i)=>i>0?1-ds(e):ds(t),translate:(t,e)=>e<0?[{transform:us(100*t),zIndex:0},{transform:us(-30*(1-t)),zIndex:-1}]:[{transform:us(-30*t),zIndex:-1},{transform:us(100*(1-t)),zIndex:0}]}},Xo={mixins:[en,io,Oo,Bo,No],props:{ratio:String,minHeight:String,maxHeight:String},data:{ratio:"16:9",minHeight:void 0,maxHeight:void 0,selList:".uc-slideshow-items",attrItem:"uc-slideshow-item",selNav:".uc-slideshow-nav",Animations:Yo},watch:{list(t){Qt(t,{aspectRatio:this.ratio?this.ratio.replace(":","/"):void 0,minHeight:this.minHeight,maxHeight:this.maxHeight,minWidth:"100%",maxWidth:"100%"})}},methods:{getAdjacentSlides(){return[1,-1].map((t=>this.slides[this.getIndex(this.index+t)]))}}},Jo={mixins:[en,Nn],props:{group:String,threshold:Number,clsItem:String,clsPlaceholder:String,clsDrag:String,clsDragState:String,clsBase:String,clsNoDrag:String,clsEmpty:String,clsCustom:String,handle:String},data:{group:!1,threshold:5,clsItem:"uc-sortable-item",clsPlaceholder:"uc-sortable-placeholder",clsDrag:"uc-sortable-drag",clsDragState:"uc-drag",clsBase:"uc-sortable",clsNoDrag:"uc-sortable-nodrag",clsEmpty:"uc-sortable-empty",clsCustom:"",handle:!1,pos:{}},created(){for(const t of["init","start","move","end"]){const e=this[t];this[t]=t=>{m(this.pos,Zt(t)),e(t)}}},events:{name:Ze,passive:!1,handler:"init"},computed:{target:(t,e)=>(e.tBodies||[e])[0],items(){return St(this.target)},isEmpty(){return!this.items.length},handles({handle:t},e){return t?Ee(t,e):this.items}},watch:{isEmpty(t){lt(this.target,this.clsEmpty,t)},handles(t,e){Qt(e,{touchAction:"",userSelect:""}),Qt(t,{touchAction:"none",userSelect:"none"})}},update:{write(t){if(!this.drag||!vt(this.placeholder))return;const{pos:{x:e,y:i},origin:{offsetTop:n,offsetLeft:s},placeholder:o}=this;Qt(this.drag,{top:i-n,left:e-s});const r=this.getSortable(document.elementFromPoint(e,i));if(!r)return;const{items:a}=r;if(a.some(se.inProgress))return;const l=function(t,e){return t[u(t,(t=>Y(e,Pe(t))))]}(a,{x:e,y:i});if(a.length&&(!l||l===o))return;const h=this.getSortable(o),c=function(t,e,i,n,s,o){if(!St(t).length)return;const r=Pe(e);if(!o)return function(t,e){const i=1===St(t).length;i&&me(t,e);const n=St(t),s=n.some(((t,e)=>{const i=Pe(t);return n.slice(e+1).some((t=>{const e=Pe(t);return!Zo([i.left,i.right],[e.left,e.right])}))}));return i&&be(e),s}(t,i)||s<r.top+r.height/2?e:e.nextElementSibling;const a=Pe(i),l=Zo([r.top,r.bottom],[a.top,a.bottom]),[h,c,d,u]=l?[n,"width","left","right"]:[s,"height","top","bottom"],f=a[c]<r[c]?r[c]-a[c]:0;return a[d]<r[d]?!(f&&h<r[d]+f)&&e.nextElementSibling:!(f&&h>r[u]-f)&&e}(r.target,l,o,e,i,r===h&&t.moved!==l);!1!==c&&(c&&o===c||(r!==h?(h.remove(o),t.moved=l):delete t.moved,r.insert(o,c),this.touched.add(r)))},events:["move"]},methods:{init(t){const{target:e,button:i,defaultPrevented:n}=t,[s]=this.items.filter((t=>t.contains(e)));!s||n||i>0||pt(e)||e.closest(`.${this.clsNoDrag}`)||this.handle&&!e.closest(this.handle)||(t.preventDefault(),this.touched=new Set([this]),this.placeholder=s,this.origin={target:e,index:It(s),...this.pos},Lt(document,Ke,this.move),Lt(document,Qe,this.end),this.threshold||this.start(t))},start(t){this.drag=function(t,e){let i;if(de(e,"li","tr")){i=Te("<div>"),me(i,e.cloneNode(!0).children);for(const t of e.getAttributeNames())Q(i,t,e.getAttribute(t))}else i=e.cloneNode(!0);return me(t,i),Qt(i,"margin","0","important"),Qt(i,{boxSizing:"border-box",width:e.offsetWidth,height:e.offsetHeight,padding:Qt(e,"padding")}),Oe(i.firstElementChild,Oe(e.firstElementChild)),i}(this.$container,this.placeholder);const{left:e,top:i}=Pe(this.placeholder);m(this.origin,{offsetLeft:this.pos.x-e,offsetTop:this.pos.y-i}),nt(this.drag,this.clsDrag,this.clsCustom),nt(this.placeholder,this.clsPlaceholder),nt(this.items,this.clsItem),nt(document.documentElement,this.clsDragState),Vt(this.$el,"start",[this,this.placeholder]),function(t){let e=Date.now();Go=setInterval((()=>{let{x:i,y:n}=t;n+=document.scrollingElement.scrollTop;const s=.3*(Date.now()-e);e=Date.now(),Di(document.elementFromPoint(i,t.y)).reverse().some((t=>{let{scrollTop:e,scrollHeight:i}=t;const{top:o,bottom:r,height:a}=_i(t);if(o<n&&o+35>n)e-=s;else{if(!(r>n&&r-35<n))return;e+=s}if(e>0&&e<i-a)return t.scrollTop=e,!0}))}),15)}(this.pos),this.move(t)},move(t){this.drag?this.$emit("move"):(Math.abs(this.pos.x-this.origin.x)>this.threshold||Math.abs(this.pos.y-this.origin.y)>this.threshold)&&this.start(t)},end(){if(Wt(document,Ke,this.move),Wt(document,Qe,this.end),!this.drag)return;clearInterval(Go);const t=this.getSortable(this.placeholder);this===t?this.origin.index!==It(this.placeholder)&&Vt(this.$el,"moved",[this,this.placeholder]):(Vt(t.$el,"added",[t,this.placeholder]),Vt(this.$el,"removed",[this,this.placeholder])),Vt(this.$el,"stop",[this,this.placeholder]),be(this.drag),this.drag=null;for(const{clsPlaceholder:t,clsItem:e}of this.touched)for(const i of this.touched)st(i.items,t,e);this.touched=null,st(document.documentElement,this.clsDragState)},insert(t,e){nt(this.items,this.clsItem);this.animate((()=>e?ge(e,t):me(this.target,t)))},remove(t){this.target.contains(t)&&this.animate((()=>be(t)))},getSortable(t){do{const e=this.$getComponent(t,"sortable");if(e&&(e===this||!1!==this.group&&e.group===this.group))return e}while(t=vt(t))}}};let Go;function Zo(t,e){return t[1]>e[0]&&e[1]>t[0]}var Ko={props:{pos:String,offset:null,flip:Boolean,shift:Boolean,inset:Boolean},data:{pos:"bottom-"+(Xe?"right":"left"),offset:!1,flip:!0,shift:!0,inset:!1},connected(){this.pos=this.$props.pos.split("-").concat("center").slice(0,2),[this.dir,this.align]=this.pos,this.axis=d(["top","bottom"],this.dir)?"y":"x"},methods:{positionAt(t,e,i){let n=[this.getPositionOffset(t),this.getShiftOffset(t)];const s=[this.flip&&"flip",this.shift&&"shift"],o={element:[this.inset?this.dir:Fe(this.dir),this.align],target:[this.dir,this.align]};if("y"===this.axis){for(const t in o)o[t].reverse();n.reverse(),s.reverse()}const r=function(t){const e=Pi(t),{scrollTop:i}=e;return()=>{i!==e.scrollTop&&(e.scrollTop=i)}}(t),a=Pe(t);Qt(t,{top:-a.height,left:-a.width}),Fi(t,e,{attach:o,offset:n,boundary:i,placement:s,viewportOffset:this.getViewportOffset(t)}),r()},getPositionOffset(t=this.$el){return je(!1===this.offset?Qt(t,"--uc-position-offset"):this.offset,"x"===this.axis?"width":"height",t)*(d(["left","top"],this.dir)?-1:1)*(this.inset?-1:1)},getShiftOffset(t=this.$el){return"center"===this.align?0:je(Qt(t,"--uc-position-shift-offset"),"y"===this.axis?"width":"height",t)*(d(["left","top"],this.align)?1:-1)},getViewportOffset:t=>je(Qt(t,"--uc-position-viewport-offset"))}};var Qo={mixins:[Qn,ts,Ko],data:{pos:"top",animation:["uc-animation-scale-up"],duration:100,cls:"uc-active"},connected(){var t;gt(t=this.$el)||Q(t,"tabindex","0")},disconnected(){this.hide()},methods:{show(){if(this.isToggled(this.tooltip||null))return;const{delay:t=0,title:e}=function(t){const{el:e,id:i,data:n}=t;return["delay","title"].reduce(((t,i)=>({[i]:it(e,i),...t})),{...cn(it(e,i),["title"]),...n})}(this.$options);if(!e)return;const i=Q(this.$el,"title"),n=Lt(this.$el,["blur",ei],(t=>!Gt(t)&&this.hide()));this.reset=()=>{Q(this.$el,{title:i,"aria-describedby":null}),n()};const s=Ks(this);Q(this.$el,{title:null,"aria-describedby":s}),clearTimeout(this.showTimer),this.showTimer=setTimeout((()=>this._show(e,s)),t)},async hide(){var t;bt(this.$el,"input:focus")||(clearTimeout(this.showTimer),this.isToggled(this.tooltip||null)&&await this.toggleElement(this.tooltip,!1,!1),null==(t=this.reset)||t.call(this),be(this.tooltip),this.tooltip=null)},async _show(t,e){this.tooltip=me(this.container,`<div id="${e}" class="uc-${this.$options.name}" role="tooltip"> <div class="uc-${this.$options.name}-inner">${t}</div> </div>`),Lt(this.tooltip,"toggled",((t,e)=>{if(!e)return;const i=()=>this.positionAt(this.tooltip,this.$el);i();const[n,s]=function(t,e,[i,n]){const s=Me(t),o=Me(e),r=[["left","right"],["top","bottom"]];for(const t of r){if(s[t[0]]>=o[t[1]]){i=t[1];break}if(s[t[1]]<=o[t[0]]){i=t[0];break}}return n=(d(r[0],i)?r[1]:r[0]).find((t=>s[t]===o[t]))||"center",[i,n]}(this.tooltip,this.$el,this.pos);this.origin="y"===this.axis?`${Fe(n)}-${s}`:`${s}-${Fe(n)}`;const o=[qt(document,`keydown ${Ze}`,this.hide,!1,(t=>t.type===Ze&&!this.$el.contains(t.target)||"keydown"===t.type&&t.keyCode===Hn)),Lt([document,...Mi(this.$el)],"scroll",i,{passive:!0})];qt(this.tooltip,"hide",(()=>o.forEach((t=>t()))),{self:!0})})),await this.toggleElement(this.tooltip,!0)||this.hide()}},events:{[`focus ${ti} ${Ze}`](t){(!Gt(t)||t.type===Ze)&&this.show()}}};var tr={mixins:[ms],i18n:{invalidMime:"Invalid File Type: %s",invalidName:"Invalid File Name: %s",invalidSize:"Invalid File Size: %s Kilobytes Max"},props:{allow:String,clsDragover:String,concurrent:Number,maxSize:Number,method:String,mime:String,multiple:Boolean,name:String,params:Object,type:String,url:String},data:{allow:!1,clsDragover:"uc-dragover",concurrent:1,maxSize:0,method:"POST",mime:!1,multiple:!1,name:"files[]",params:{},type:"",url:"",abort:R,beforeAll:R,beforeSend:R,complete:R,completeAll:R,error:R,fail:R,load:R,loadEnd:R,loadStart:R,progress:R},events:{change(t){bt(t.target,'input[type="file"]')&&(t.preventDefault(),t.target.files&&this.upload(t.target.files),t.target.value="")},drop(t){ir(t);const e=t.dataTransfer;null!=e&&e.files&&(st(this.$el,this.clsDragover),this.upload(e.files))},dragenter(t){ir(t)},dragover(t){ir(t),nt(this.$el,this.clsDragover)},dragleave(t){ir(t),st(this.$el,this.clsDragover)}},methods:{async upload(t){if(!(t=p(t)).length)return;Vt(this.$el,"upload",[t]);for(const e of t){if(this.maxSize&&1e3*this.maxSize<e.size)return void this.fail(this.t("invalidSize",this.maxSize));if(this.allow&&!er(this.allow,e.name))return void this.fail(this.t("invalidName",this.allow));if(this.mime&&!er(this.mime,e.type))return void this.fail(this.t("invalidMime",this.mime))}this.multiple||(t=t.slice(0,1)),this.beforeAll(this,t);const e=function(t,e){const i=[];for(let n=0;n<t.length;n+=e)i.push(t.slice(n,n+e));return i}(t,this.concurrent),i=async t=>{const n=new FormData;t.forEach((t=>n.append(this.name,t)));for(const t in this.params)n.append(t,this.params[t]);try{const t=await function(t,e){const i={data:null,method:"GET",headers:{},xhr:new XMLHttpRequest,beforeSend:R,responseType:"",...e};return Promise.resolve().then((()=>i.beforeSend(i))).then((()=>function(t,e){return new Promise(((i,n)=>{const{xhr:s}=e;for(const t in e)if(t in s)try{s[t]=e[t]}catch{}s.open(e.method.toUpperCase(),t);for(const t in e.headers)s.setRequestHeader(t,e.headers[t]);Lt(s,"load",(()=>{0===s.status||s.status>=200&&s.status<300||304===s.status?i(s):n(m(Error(s.statusText),{xhr:s,status:s.status}))})),Lt(s,"error",(()=>n(m(Error("Network Error"),{xhr:s})))),Lt(s,"timeout",(()=>n(m(Error("Network Timeout"),{xhr:s})))),s.send(e.data)}))}(t,i)))}(this.url,{data:n,method:this.method,responseType:this.type,beforeSend:t=>{const{xhr:e}=t;Lt(e.upload,"progress",this.progress);for(const t of["loadStart","load","loadEnd","abort"])Lt(e,t.toLowerCase(),this[t]);return this.beforeSend(t)}});this.complete(t),e.length?await i(e.shift()):this.completeAll(t)}catch(t){this.error(t)}};await i(e.shift())}}};function er(t,e){return e.match(new RegExp(`^${t.replace(/\//g,"\\/").replace(/\*\*/g,"(\\/[^\\/]+)*").replace(/\*/g,"[^\\/]+").replace(/((?!\\))\?/g,"$1.")}$`,"i"))}function ir(t){t.preventDefault(),t.stopPropagation()}var nr,sr={connected(){var t;this.registerObserver(ui((null==(t=this.$options.resizeTargets)?void 0:t.call(this))||this.$el,(()=>this.$emit("resize"))))}},or={mixins:[en,ho,sr],props:{...Object.fromEntries(["duration","x","y","translateX","translateY","color","background"].map((t=>[t,null]))),y:"list"},setup(t){const e=t.x,i=t.y;console.log(e,i)},data:{animation:void 0},computed:{animeOptions:t=>({...t,color:void 0})},connected(){this.animation=t(this.animeOptions),this.animation.pause()},disconnected(){ye(this.wrapper.childNodes)},update:{read(){return{hide:!this.matchMedia}},write({hide:t}){t?"reset"in this.animation&&"function"==typeof this.animation.reset&&this.animation.reset():this.animation.restart()},events:["resize"]}},rr=Object.freeze({__proto__:null,Anime:or,Countdown:sn,Filter:Un,Lightbox:ro,LightboxPanel:so,Notification:lo,Parallax:Mo,Slider:qo,SliderParallax:Vo,Slideshow:Xo,SlideshowParallax:Vo,Sortable:Jo,Tooltip:Qo,Upload:tr});function ar(t){Vt(document,"uikit:init",t),document.body&&Ce(document.body,cr),new MutationObserver((t=>t.forEach(lr))).observe(document,{subtree:!0,childList:!0}),new MutationObserver((t=>t.forEach(hr))).observe(document,{subtree:!0,attributes:!0}),t._initialized=!0}function lr({addedNodes:t,removedNodes:e}){for(const e of t)Ce(e,cr);for(const t of e)Ce(t,dr)}function hr({target:t,attributeName:e}){var i;const n=ur(e);if(n){if(tt(t,e))return void Us(n,t);null==(i=Xs(t,n))||i.$destroy()}}function cr(t){const e=Ys(t);for(const i in Ys(t))zs(e[i]);for(const e of t.getAttributeNames()){const i=ur(e);i&&Us(i,t)}}function dr(t){const e=Ys(t);for(const i in Ys(t))Hs(e[i])}function ur(t){h(t,"data-")&&(t=t.slice(5));const e=Vs[t];return e&&(w(e)?e:e.options).name}(function(t){let e;t.component=Rs,t.getComponents=Ys,t.getComponent=Xs,t.update=Js,t.use=function(t){if(!t.installed)return t.call(null,this),t.installed=!0,this},t.mixin=function(t,e){(e=(k(e)?this.component(e):e)||this).options=hn(e.options,t)},t.extend=function(t){t||(t={});const e=this,i=function(t){js(this,t)};return(i.prototype=Object.create(e.prototype)).constructor=i,i.options=hn(e.options,t),i.super=e,i.extend=e.extend,i},Object.defineProperty(t,"container",{get:()=>e||document.body,set(t){e=Te(t)}})})(Ls),(nr=Ls).prototype.$mount=function(t){const e=this;(function(t,e){t[qs]||(t[qs]={}),t[qs][e.$options.name]=e})(t,e),e.$options.el=t,document.contains(t)&&zs(e)},nr.prototype.$destroy=function(t=!1){const e=this,{el:i}=e.$options;i&&Hs(e),Ns(e,"destroy"),function(t,e){var i;null==(i=t[qs])||delete i[e.$options.name],E(t[qs])||delete t[qs]}(i,e),t&&be(e.$el)},nr.prototype.$create=Us,nr.prototype.$emit=function(t){fn(this,t)},nr.prototype.$update=function(t=this.$el,e){Js(t,e)},nr.prototype.$reset=function(){Hs(this),zs(this)},nr.prototype.$getComponent=Xs,Object.defineProperties(nr.prototype,{$el:{get(){return this.$options.el}},$container:Object.getOwnPropertyDescriptor(nr,"container")});var fr={mixins:[en,ts],props:{animation:Boolean,targets:String,active:null,collapsible:Boolean,multiple:Boolean,toggle:String,content:String,offset:Number},data:{targets:"> *",active:!1,animation:!0,collapsible:!0,multiple:!1,clsOpen:"uc-open",toggle:"> .uc-accordion-title",content:"> .uc-accordion-content",offset:0},computed:{items:({targets:t},e)=>Ee(t,e),toggles({toggle:t}){return this.items.map((e=>Te(t,e)))},contents({content:t}){return this.items.map((e=>{var i;return(null==(i=e._wrapper)?void 0:i.firstElementChild)||Te(t,e)}))}},watch:{items(t,e){if(e||at(t,this.clsOpen))return;const i=!1!==this.active&&t[Number(this.active)]||!this.collapsible&&t[0];i&&this.toggle(i,!1)},toggles(){this.$emit()},contents(t){for(const e of t){const t=at(this.items.find((t=>t.contains(e))),this.clsOpen);pr(e,!t)}this.$emit()}},observe:vn(),events:[{name:"click keydown",delegate(){return`${this.targets} ${this.$props.toggle}`},async handler(t){var e;"keydown"===t.type&&t.keyCode!==Fn||(t.preventDefault(),null==(e=this._off)||e.call(this),this._off=function(t){const e=Pi(t,!0);let i;return function n(){i=requestAnimationFrame((()=>{const{top:i}=Pe(t);i<0&&(e.scrollTop+=i),n()}))}(),()=>requestAnimationFrame((()=>cancelAnimationFrame(i)))}(t.target),await this.toggle(It(this.toggles,t.current)),this._off())}},{name:"shown hidden",self:!0,delegate(){return this.targets},handler(){this.$emit()}}],update(){const t=wt(this.items,`.${this.clsOpen}`);for(const e in this.items){const i=this.toggles[e],n=this.contents[e];if(!i||!n)continue;i.id=Ks(this,i),n.id=Ks(this,n);const s=d(t,this.items[e]);Q(i,{role:de(i,"a")?"button":null,"aria-controls":n.id,"aria-expanded":s,"aria-disabled":!this.collapsible&&t.length<2&&s}),Q(n,{role:"region","aria-labelledby":i.id}),de(n,"ul")&&Q(St(n),"role","presentation")}},methods:{toggle(t,e){let i=[t=this.items[Z(t,this.items)]];const n=wt(this.items,`.${this.clsOpen}`);if(!this.multiple&&!d(n,i[0])&&(i=i.concat(n)),!(!this.collapsible&&n.length<2&&d(n,t)))return Promise.all(i.map((t=>this.toggleElement(t,!d(n,t),((t,i)=>{if(lt(t,this.clsOpen,i),!1!==e&&this.animation)return async function(t,e,{content:i,duration:n,velocity:s,transition:o}){var r;i=(null==(r=t._wrapper)?void 0:r.firstElementChild)||Te(i,t),t._wrapper||(t._wrapper=xe(i,"<div>"));const a=t._wrapper;Qt(a,"overflow","hidden");const l=M(Qt(a,"height"));await se.cancel(a),pr(i,!1);const h=L(["marginTop","marginBottom"],(t=>Qt(i,t)))+Pe(i).height,c=l/h;n=(s*h+n)*(e?1-c:c),Qt(a,"height",l),await se.start(a,{height:e?h:0},n,o),ye(i),delete t._wrapper,e||pr(i,!0)}(t,i,this);pr(Te(this.content,t),!i)})))))}}};function pr(t,e){t&&(t.hidden=e)}var mr={mixins:[en,ts],args:"animation",props:{animation:Boolean,close:String},data:{animation:!0,selClose:".uc-alert-close",duration:150},events:{name:"click",delegate(){return this.selClose},handler(t){t.preventDefault(),this.close()}},methods:{async close(){await this.toggleElement(this.$el,!1,gr),this.$destroy(!0)}}};function gr(t,e,{duration:i,transition:n,velocity:s}){const o=M(Qt(t,"height"));return Qt(t,"height",o),se.start(t,{height:0,marginTop:0,marginBottom:0,paddingTop:0,paddingBottom:0,borderTop:0,borderBottom:0,opacity:0},s*o+i,n)}var vr={args:"autoplay",props:{automute:Boolean,autoplay:Boolean},data:{automute:!1,autoplay:!0},connected(){"inview"===this.autoplay&&!tt(this.$el,"preload")&&(this.$el.preload="none"),de(this.$el,"iframe")&&!tt(this.$el,"allow")&&(this.$el.allow="autoplay"),this.automute&&wi(this.$el)},observe:[mn({filter:({$el:t,autoplay:e})=>e&&bi(t),handler([{isIntersecting:t}]){document.fullscreenElement||(t?gi(this.$el):vi(this.$el))},args:{intersecting:!1},options:({$el:t,autoplay:e})=>({root:"inview"===e?null:vt(t)})})]},wr={mixins:[vr],props:{width:Number,height:Number},data:{automute:!0},events:{"load loadedmetadata"(){this.$emit("resize")}},observe:pn({target:({$el:t})=>[br(t)||vt(t)],filter:({$el:t})=>!xr(t)}),update:{read(){if(xr(this.$el))return;const{ratio:t,cover:e}=G,{$el:i,width:n,height:s}=this;let o={width:n,height:s};if(!n||!s){const e={width:i.naturalWidth||i.videoWidth||i.clientWidth,height:i.naturalHeight||i.videoHeight||i.clientHeight};o=n?t(e,"width",n):s?t(e,"height",s):e}const{offsetHeight:r,offsetWidth:a}=br(i)||vt(i),l=e(o,{width:a+(a%2?1:0),height:r+(r%2?1:0)});return!(!l.width||!l.height)&&l},write({height:t,width:e}){Qt(this.$el,{height:t,width:e})},events:["resize"]}};function br(t){for(;t=vt(t);)if("static"!==Qt(t,"position"))return t}function xr(t){return de(t,"img","video")}let $r;var yr={mixins:[Qn,Ko,ts],args:"pos",props:{mode:"list",toggle:Boolean,boundary:Boolean,boundaryX:Boolean,boundaryY:Boolean,target:Boolean,targetX:Boolean,targetY:Boolean,stretch:Boolean,delayShow:Number,delayHide:Number,autoUpdate:Boolean,clsDrop:String,animateOut:Boolean,bgScroll:Boolean,closeOnScroll:Boolean},data:{mode:["click","hover"],toggle:"- *",boundary:!1,boundaryX:!1,boundaryY:!1,target:!1,targetX:!1,targetY:!1,stretch:!1,delayShow:0,delayHide:800,autoUpdate:!0,clsDrop:!1,animateOut:!1,bgScroll:!0,animation:["uc-animation-fade"],cls:"uc-open",container:!1,closeOnScroll:!1},computed:{boundary:({boundary:t,boundaryX:e,boundaryY:i},n)=>[Tt(e||t,n)||window,Tt(i||t,n)||window],target({target:t,targetX:e,targetY:i},n){return e||(e=t||this.targetEl),i||(i=t||this.targetEl),[!0===e?window:Tt(e,n),!0===i?window:Tt(i,n)]}},created(){this.tracker=new hi},beforeConnect(){this.clsDrop=this.$props.clsDrop||`uc-${this.$options.name}`},connected(){nt(this.$el,"uc-drop",this.clsDrop),this.toggle&&!this.targetEl&&(this.targetEl=function(t){const{$el:e}=t.$create("toggle",Tt(t.toggle,t.$el),{target:t.$el,mode:t.mode});return Q(e,"aria-haspopup",!0),e}(this)),this._style=q(this.$el.style,["width","height"])},disconnected(){this.isActive()&&(this.hide(!1),$r=null),Qt(this.$el,this._style)},observe:vn({target:({toggle:t,$el:e})=>Tt(t,e),targets:({$el:t})=>t}),events:[{name:"click",delegate:()=>".uc-drop-close",handler(t){t.preventDefault(),this.hide(!1)}},{name:"click",delegate:()=>'a[href*="#"]',handler({defaultPrevented:t,current:e}){const{hash:i}=e;!t&&i&&kt(e)&&!this.$el.contains(Te(i))&&this.hide(!1)}},{name:"beforescroll",handler(){this.hide(!1)}},{name:"toggle",self:!0,handler(t,e){t.preventDefault(),this.isToggled()?this.hide(!1):this.show(null==e?void 0:e.$el,!1)}},{name:"toggleshow",self:!0,handler(t,e){t.preventDefault(),this.show(null==e?void 0:e.$el)}},{name:"togglehide",self:!0,handler(t){t.preventDefault(),bt(this.$el,":focus,:hover")||this.hide()}},{name:`${ti} focusin`,filter(){return d(this.mode,"hover")},handler(t){Gt(t)||this.clearTimers()}},{name:`${ei} focusout`,filter(){return d(this.mode,"hover")},handler(t){!Gt(t)&&t.relatedTarget&&this.hide()}},{name:"toggled",self:!0,handler(t,e){e&&(this.clearTimers(),this.position())}},{name:"show",self:!0,handler(){$r=this,this.tracker.init(),Q(this.targetEl,"aria-expanded",!0);const t=[Sr(this),kr(this),Tr(this),this.autoUpdate&&Ir(this),this.closeOnScroll&&Cr(this)];qt(this.$el,"hide",(()=>t.forEach((t=>t&&t()))),{self:!0}),this.bgScroll||qt(this.$el,"hidden",Kn(this.$el),{self:!0})}},{name:"beforehide",self:!0,handler:"clearTimers"},{name:"hide",handler({target:t}){this.$el===t?($r=this.isActive()?null:$r,this.tracker.cancel(),Q(this.targetEl,"aria-expanded",null)):$r=null===$r&&this.$el.contains(t)&&this.isToggled()?this:$r}}],update:{write(){this.isToggled()&&!at(this.$el,this.clsEnter)&&this.position()}},methods:{show(t=this.targetEl,e=!0){if(this.isToggled()&&t&&this.targetEl&&t!==this.targetEl&&this.hide(!1,!1),this.targetEl=t,this.clearTimers(),!this.isActive()){if($r){if(e&&$r.isDelaying)return void(this.showTimer=setTimeout((()=>bt(t,":hover")&&this.show()),10));let i;for(;$r&&i!==$r&&!$r.$el.contains(this.$el);)i=$r,$r.hide(!1,!1)}this.container&&vt(this.$el)!==this.container&&me(this.container,this.$el),this.showTimer=setTimeout((()=>this.toggleElement(this.$el,!0)),e&&this.delayShow||0)}},hide(t=!0,e=!0){const i=()=>this.toggleElement(this.$el,!1,this.animateOut&&e);this.clearTimers(),this.isDelayedHide=t,this.isDelaying=function(t){const e=[];return Ce(t,(t=>"static"!==Qt(t,"position")&&e.push(t))),e}(this.$el).some((t=>this.tracker.movesTo(t))),t&&this.isDelaying?this.hideTimer=setTimeout(this.hide,50):t&&this.delayHide?this.hideTimer=setTimeout(i,this.delayHide):i()},clearTimers(){clearTimeout(this.showTimer),clearTimeout(this.hideTimer),this.showTimer=null,this.hideTimer=null,this.isDelaying=!1},isActive(){return $r===this},position(){st(this.$el,"uc-drop-stack"),Qt(this.$el,this._style),this.$el.hidden=!0;const t=this.target.map((t=>function(t,e){return _i(Mi(e).find((e=>e.contains(t))))}(this.$el,t))),e=this.getViewportOffset(this.$el),i=[[0,["x","width","left","right"]],[1,["y","height","top","bottom"]]];for(const[n,[s,o]]of i)this.axis!==s&&d([s,!0],this.stretch)&&Qt(this.$el,{[o]:Math.min(Me(this.boundary[n])[o],t[n][o]-2*e),[`overflow-${s}`]:"auto"});const n=t[0].width-2*e;this.$el.hidden=!1,Qt(this.$el,"maxWidth",""),this.$el.offsetWidth>n&&nt(this.$el,"uc-drop-stack"),Qt(this.$el,"maxWidth",n),this.positionAt(this.$el,this.target,this.boundary);for(const[n,[s,o,r,a]]of i)if(this.axis===s&&d([s,!0],this.stretch)){const i=Math.abs(this.getPositionOffset()),l=Me(this.target[n]),h=Me(this.$el);Qt(this.$el,{[o]:(l[r]>h[r]?l[this.inset?a:r]-Math.max(Me(this.boundary[n])[r],t[n][r]+e):Math.min(Me(this.boundary[n])[a],t[n][a]-e)-l[this.inset?r:a])-i,[`overflow-${s}`]:"auto"}),this.positionAt(this.$el,this.target,this.boundary)}}}};function Sr(t){const e=()=>t.$emit(),i=[fi(e),ui(Mi(t.$el).concat(t.target),e)];return()=>i.map((t=>t.disconnect()))}function Ir(t,e=()=>t.$emit()){return Lt([document,...Mi(t.$el)],"scroll",e,{passive:!0})}function kr(t){return Lt(document,"keydown",(e=>{e.keyCode===Hn&&t.hide(!1)}))}function Cr(t){return Ir(t,(()=>t.hide(!1)))}function Tr(t){return Lt(document,Ze,(({target:e})=>{t.$el.contains(e)||qt(document,`${Qe} ${ii} scroll`,(({defaultPrevented:i,type:n,target:s})=>{!i&&n===Qe&&e===s&&(!t.targetEl||!$t(e,t.targetEl))&&t.hide(!1)}),!0)}))}var Er={mixins:[en,Qn],props:{align:String,clsDrop:String,boundary:Boolean,dropbar:Boolean,dropbarAnchor:Boolean,duration:Number,mode:Boolean,offset:Boolean,stretch:Boolean,delayShow:Boolean,delayHide:Boolean,target:Boolean,targetX:Boolean,targetY:Boolean,animation:Boolean,animateOut:Boolean,closeOnScroll:Boolean},data:{align:Xe?"right":"left",clsDrop:"uc-dropdown",clsDropbar:"uc-dropnav-dropbar",boundary:!0,dropbar:!1,dropbarAnchor:!1,duration:200,container:!1,selNavItem:"> li > a, > ul > li > a"},computed:{dropbarAnchor:({dropbarAnchor:t},e)=>Tt(t,e)||e,dropbar({dropbar:t}){return t?(t=this._dropbar||Tt(t,this.$el)||Te(`+ .${this.clsDropbar}`,this.$el))||(this._dropbar=Te("<div></div>")):null},dropContainer(t,e){return this.container||e},dropdowns({clsDrop:t},e){var i;const n=Ee(`.${t}`,e);if(this.dropContainer!==e)for(const e of Ee(`.${t}`,this.dropContainer)){const t=null==(i=this.getDropdown(e))?void 0:i.targetEl;!d(n,e)&&t&&this.$el.contains(t)&&n.push(e)}return n},items:({selNavItem:t},e)=>Ee(t,e)},watch:{dropbar(t){nt(t,"uc-dropbar","uc-dropbar-top",this.clsDropbar,`uc-${this.$options.name}-dropbar`)},dropdowns(){this.initializeDropdowns()}},connected(){this.initializeDropdowns()},disconnected(){be(this._dropbar),delete this._dropbar},events:[{name:"mouseover focusin",delegate(){return this.selNavItem},handler({current:t}){const e=this.getActive();e&&d(e.mode,"hover")&&e.targetEl&&!t.contains(e.targetEl)&&!e.isDelaying&&e.hide(!1)}},{name:"keydown",self:!0,delegate(){return this.selNavItem},handler(t){var e;const{current:i,keyCode:n}=t,s=this.getActive();n===Rn&&(null==s?void 0:s.targetEl)===i&&(t.preventDefault(),null==(e=Te(mt,s.$el))||e.focus()),Ar(t,this.items,s)}},{name:"keydown",el(){return this.dropContainer},delegate(){return`.${this.clsDrop}`},handler(t){var e;const{current:i,keyCode:n}=t;if(!d(this.dropdowns,i))return;const s=this.getActive();let o=-1;if(n===Ln?o=0:n===jn?o="last":n===qn?o="previous":n===Rn?o="next":n===Hn&&(null==(e=s.targetEl)||e.focus()),~o){t.preventDefault();const e=Ee(mt,i);e[Z(o,e,u(e,(t=>bt(t,":focus"))))].focus()}Ar(t,this.items,s)}},{name:"mouseleave",el(){return this.dropbar},filter(){return this.dropbar},handler(){const t=this.getActive();t&&d(t.mode,"hover")&&!this.dropdowns.some((t=>bt(t,":hover")))&&t.hide()}},{name:"beforeshow",el(){return this.dropContainer},filter(){return this.dropbar},handler({target:t}){this.isDropbarDrop(t)&&(this.dropbar.previousElementSibling!==this.dropbarAnchor&&ve(this.dropbarAnchor,this.dropbar),nt(t,`${this.clsDrop}-dropbar`))}},{name:"show",el(){return this.dropContainer},filter(){return this.dropbar},handler({target:t}){if(!this.isDropbarDrop(t))return;const e=this.getDropdown(t),i=()=>{const i=Math.max(...yt(t,`.${this.clsDrop}`).concat(t).map((t=>Me(t).bottom)));Me(this.dropbar,{left:Me(this.dropbar).left,top:this.getDropbarOffset(e.getPositionOffset())}),this.transitionTo(i-Me(this.dropbar).top+M(Qt(t,"marginBottom")),t)};this._observer=ui([e.$el,...e.target],i),i()}},{name:"beforehide",el(){return this.dropContainer},filter(){return this.dropbar},handler(t){const e=this.getActive();bt(this.dropbar,":hover")&&e.$el===t.target&&this.isDropbarDrop(e.$el)&&d(e.mode,"hover")&&e.isDelayedHide&&!this.items.some((t=>e.targetEl!==t&&bt(t,":focus")))&&t.preventDefault()}},{name:"hide",el(){return this.dropContainer},filter(){return this.dropbar},handler({target:t}){var e;if(!this.isDropbarDrop(t))return;null==(e=this._observer)||e.disconnect();const i=this.getActive();(!i||i.$el===t)&&this.transitionTo(0)}}],methods:{getActive(){var t;return d(this.dropdowns,null==(t=$r)?void 0:t.$el)&&$r},async transitionTo(t,e){const{dropbar:i}=this,n=Oe(i);if(e=n<t&&e,await se.cancel([e,i]),e){const s=Me(e).top-Me(i).top-n;s>0&&Qt(e,"transitionDelay",s/t*this.duration+"ms")}Qt(e,"clipPath",`polygon(0 0,100% 0,100% ${n}px,0 ${n}px)`),Oe(i,n),await Promise.all([se.start(i,{height:t},this.duration),se.start(e,{clipPath:`polygon(0 0,100% 0,100% ${t}px,0 ${t}px)`},this.duration).finally((()=>Qt(e,{clipPath:"",transitionDelay:""})))]).catch(R)},getDropdown(t){return this.$getComponent(t,"drop")||this.$getComponent(t,"dropdown")},isDropbarDrop(t){return d(this.dropdowns,t)&&at(t,this.clsDrop)},getDropbarOffset(t){const{$el:e,target:i,targetY:n}=this,{top:s,height:o}=Me(Tt(n||i||e,e));return s+o+t},initializeDropdowns(){this.$create("drop",this.dropdowns.filter((t=>!this.getDropdown(t))),{...this.$props,flip:!1,shift:!0,pos:`bottom-${this.align}`,boundary:!0===this.boundary?this.$el:this.boundary})}}};function Ar(t,e,i){var n,s,o;const{current:r,keyCode:a}=t;let l=-1;a===Ln?l=0:a===jn?l="last":a===Wn?l="previous":a===Vn?l="next":a===zn&&(null==(n=i.targetEl)||n.focus(),null==(s=i.hide)||s.call(i,!1)),~l&&(t.preventDefault(),null==(o=i.hide)||o.call(i,!1),e[Z(l,e,e.indexOf(i.targetEl||r))].focus())}var Dr={mixins:[en],args:"target",props:{target:Boolean},data:{target:!1},computed:{input:(t,e)=>Te(ft,e),state(){return this.input.nextElementSibling},target({target:t},e){return t&&(!0===t&&vt(this.input)===e&&this.input.nextElementSibling||Te(t,e))}},update(){var t;const{target:e,input:i}=this;if(!e)return;let n;const s=pt(e)?"value":"textContent",o=e[s],r=null!=(t=i.files)&&t[0]?i.files[0].name:bt(i,"select")&&(n=Ee("option",i).filter((t=>t.selected))[0])?n.textContent:i.value;o!==r&&(e[s]=r)},events:[{name:"change",handler(){this.$emit()}},{name:"reset",el(){return this.$el.closest("form")},handler(){this.$emit()}}]},Pr={extends:Sn,mixins:[en],name:"grid",props:{masonry:Boolean,parallax:String,parallaxStart:String,parallaxEnd:String,parallaxJustify:Boolean},data:{margin:"uc-grid-margin",clsStack:"uc-grid-stack",masonry:!1,parallax:0,parallaxStart:0,parallaxEnd:0,parallaxJustify:!1},connected(){this.masonry&&nt(this.$el,"uc-flex-top","uc-flex-wrap-top")},observe:bn({filter:({parallax:t,parallaxJustify:e})=>t||e}),update:[{write({rows:t}){lt(this.$el,this.clsStack,!t.some((t=>t.length>1)))},events:["resize"]},{read(t){const{rows:e}=t;let{masonry:i,parallax:n,parallaxJustify:s,margin:o}=this;if(n=Math.max(0,je(n)),!(i||n||s)||Mr(e)||e[0].some(((t,i)=>e.some((e=>e[i]&&e[i].offsetWidth!==t.offsetWidth)))))return t.translates=t.scrollColumns=!1;let r,a,l=function(t,e){const i=t.flat().find((t=>at(t,e)));return M(i?Qt(i,"marginTop"):Qt(t[0][0],"paddingLeft"))}(e,o);i?[r,a]=function(t,e,i){const n=[],s=[],o=Array(t[0].length).fill(0);let r=0;for(let a of t){Xe&&(a=a.reverse());let t=0;for(const l in a){const{offsetWidth:h,offsetHeight:c}=a[l],d=i?l:o.indexOf(Math.min(...o));_r(n,d,a[l]),_r(s,d,[(d-l)*h*(Xe?-1:1),o[d]-r]),o[d]+=c+e,t=Math.max(t,c)}r+=t+e}return[n,s]}(e,l,"next"===i):r=function(t){const e=[];for(const i of t)for(const t in i)_r(e,t,i[t]);return e}(e);const h=r.map((t=>L(t,"offsetHeight")+l*(t.length-1))),c=Math.max(0,...h);let d,u,f;return(n||s)&&(d=h.map(((t,e)=>s?c-t+n:n/(e%2||8))),s||(n=Math.max(...h.map(((t,e)=>t+d[e]-c)))),u=je(this.parallaxStart,"height",this.$el,!0),f=je(this.parallaxEnd,"height",this.$el,!0)),{columns:r,translates:a,scrollColumns:d,parallaxStart:u,parallaxEnd:f,padding:n,height:a?c:""}},write({height:t,padding:e}){Qt(this.$el,"paddingBottom",e||""),!1!==t&&Qt(this.$el,"height",t)},events:["resize"]},{read({rows:t,scrollColumns:e,parallaxStart:i,parallaxEnd:n}){return(!e||!Mr(t))&&{scrolled:!!e&&Ai(this.$el,i,n)}},write({columns:t,scrolled:e,scrollColumns:i,translates:n}){!e&&!n||t.forEach(((t,s)=>t.forEach(((t,o)=>{let[r,a]=n&&n[s][o]||[0,0];e&&(a+=e*i[s]),Qt(t,"transform",`translate(${r}px, ${a}px)`)}))))},events:["scroll","resize"]}]};function Mr(t){return t.flat().some((t=>"absolute"===Qt(t,"position")))}function _r(t,e,i){t[e]||(t[e]=[]),t[e].push(i)}var Br={args:"target",props:{target:String,row:Boolean},data:{target:"> *",row:!0},computed:{elements:({target:t},e)=>Ee(t,e)},observe:pn({target:({$el:t,elements:e})=>e.reduce(((t,e)=>t.concat(e,...e.children)),[t])}),events:{name:"loadingdone",el:()=>document.fonts,handler(){this.$emit("resize")}},update:{read(){return{rows:(this.row?In(this.elements):[this.elements]).map(Or)}},write({rows:t}){for(const{heights:e,elements:i}of t)i.forEach(((t,i)=>Qt(t,"minHeight",e[i])))},events:["resize"]}};function Or(t){if(t.length<2)return{heights:[""],elements:t};let e=t.map(Nr);const i=Math.max(...e);return{heights:t.map(((t,n)=>e[n].toFixed(2)===i.toFixed(2)?"":i)),elements:t}}function Nr(t){const e=q(t.style,["display","minHeight"]);ut(t)||Qt(t,"display","block","important"),Qt(t,"minHeight","");const i=Pe(t).height-He(t,"height","content-box");return Qt(t,e),i}var zr={args:"target",props:{target:String},data:{target:""},computed:{target:({target:t},e)=>Tt(t,e)},observe:pn({target:({target:t})=>t}),update:{read(){return{height:this.target.offsetHeight}},write({height:t}){Qt(this.$el,{minHeight:t})},events:["resize"]}},Hr={mixins:[ho],props:{expand:Boolean,offsetTop:Boolean,offsetBottom:Boolean,minHeight:Number},data:{expand:!1,offsetTop:!1,offsetBottom:!1,minHeight:0},observe:[wn({filter:({expand:t})=>t}),pn({target:({$el:t})=>Di(t)})],update:{read(){if(!ut(this.$el))return!1;let t="";const e=He(this.$el,"height","content-box"),{body:i,scrollingElement:n}=document,s=Pi(this.$el),{height:o}=_i(s===i?n:s),r=n===s||i===s;if(t="calc("+(r?"100vh":`${o}px`),this.expand){t+=` - ${Pe(s).height-Pe(this.$el).height}px`}else{if(this.offsetTop)if(r){const e=Be(!0===this.offsetTop?this.$el:Tt(this.offsetTop,this.$el))[0]-Be(s)[0];t+=e>0&&e<o/2?` - ${e}px`:""}else t+=` - ${Qt(s,"paddingTop")}`;!0===this.offsetBottom?t+=` - ${Pe(this.$el.nextElementSibling).height}px`:T(this.offsetBottom)?t+=` - ${this.offsetBottom}vh`:this.offsetBottom&&c(this.offsetBottom,"px")?t+=` - ${M(this.offsetBottom)}px`:k(this.offsetBottom)&&(t+=` - ${Pe(Tt(this.offsetBottom,this.$el)).height}px`)}return t+=(e?` - ${e}px`:"")+")",{minHeight:t}},write({minHeight:t}){Qt(this.$el,"minHeight",`max(${this.minHeight||0}px, ${t})`)},events:["resize"]}},Fr='<svg width="20" height="20" viewBox="0 0 20 20"><circle fill="none" stroke="#000" stroke-width="1.1" cx="9" cy="9" r="7"/><path fill="none" stroke="#000" stroke-width="1.1" d="M14,14 L18,18 L14,14 Z"/></svg>',jr={args:"src",props:{width:Number,height:Number,ratio:Number},data:{ratio:1},connected(){this.svg=this.getSvg().then((t=>{if(!this._connected)return;const e=function(t,e){if(dt(e)||de(e,"canvas")){e.hidden=!0;const i=e.nextElementSibling;return Lr(t,i)?i:ve(e,t)}const i=e.lastElementChild;return Lr(t,i)?i:me(e,t)}(t,this.$el);return this.svgEl&&e!==this.svgEl&&be(this.svgEl),Wr.call(this,e,t),this.svgEl=e}),R)},disconnected(){this.svg.then((t=>{this._connected||(dt(this.$el)&&(this.$el.hidden=!1),be(t),this.svgEl=null)})),this.svg=null},methods:{async getSvg(){}}};function Lr(t,e){return de(t,"svg")&&de(e,"svg")&&t.innerHTML===e.innerHTML}function Wr(t,e){const i=["width","height"];let n=i.map((t=>this[t]));n.some((t=>t))||(n=i.map((t=>Q(e,t))));const s=Q(e,"viewBox");s&&!n.some((t=>t))&&(n=s.split(" ").slice(2)),n.forEach(((e,n)=>Q(t,i[n],M(e)*this.ratio||null)))}const qr={spinner:'<svg width="30" height="30" viewBox="0 0 30 30"><circle fill="none" stroke="#000" cx="15" cy="15" r="14"/></svg>',totop:'<svg width="18" height="10" viewBox="0 0 18 10"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 9 9 1 17 9"/></svg>',marker:'<svg width="20" height="20" viewBox="0 0 20 20"><rect x="9" y="4" width="1" height="11"/><rect x="4" y="9" width="11" height="1"/></svg>',"close-icon":'<svg width="14" height="14" viewBox="0 0 14 14"><line fill="none" stroke="#000" stroke-width="1.1" x1="1" y1="1" x2="13" y2="13"/><line fill="none" stroke="#000" stroke-width="1.1" x1="13" y1="1" x2="1" y2="13"/></svg>',"close-large":'<svg width="20" height="20" viewBox="0 0 20 20"><line fill="none" stroke="#000" stroke-width="1.4" x1="1" y1="1" x2="19" y2="19"/><line fill="none" stroke="#000" stroke-width="1.4" x1="19" y1="1" x2="1" y2="19"/></svg>',"drop-parent-icon":'<svg width="12" height="12" viewBox="0 0 12 12"><polyline fill="none" stroke="#000" stroke-width="1.1" points="1 3.5 6 8.5 11 3.5"/></svg>',"nav-parent-icon":'<svg width="12" height="12" viewBox="0 0 12 12"><polyline fill="none" stroke="#000" stroke-width="1.1" points="1 3.5 6 8.5 11 3.5"/></svg>',"nav-parent-icon-large":'<svg width="14" height="14" viewBox="0 0 14 14"><polyline fill="none" stroke="#000" stroke-width="1.1" points="1 4 7 10 13 4"/></svg>',"navbar-parent-icon":'<svg width="12" height="12" viewBox="0 0 12 12"><polyline fill="none" stroke="#000" stroke-width="1.1" points="1 3.5 6 8.5 11 3.5"/></svg>',"navbar-toggle-icon":'<svg width="20" height="20" viewBox="0 0 20 20"><style>.uc-navbar-toggle-icon svg&gt;[class*=&quot;line-&quot;]{transition:0.2s ease-in-out;transition-property:transform, opacity;transform-origin:center;opacity:1}.uc-navbar-toggle-icon svg&gt;.line-3{opacity:0}.uc-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-3{opacity:1}.uc-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-2{transform:rotate(45deg)}.uc-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-3{transform:rotate(-45deg)}.uc-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-1,.uc-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-4{opacity:0}.uc-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-1{transform:translateY(6px) scaleX(0)}.uc-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-4{transform:translateY(-6px) scaleX(0)}</style><rect class="line-1" y="3" width="20" height="2"/><rect class="line-2" y="9" width="20" height="2"/><rect class="line-3" y="9" width="20" height="2"/><rect class="line-4" y="15" width="20" height="2"/></svg>',"overlay-icon":'<svg width="40" height="40" viewBox="0 0 40 40"><rect x="19" y="0" width="1" height="40"/><rect x="0" y="19" width="40" height="1"/></svg>',"pagination-next":'<svg width="7" height="12" viewBox="0 0 7 12"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 1 6 6 1 11"/></svg>',"pagination-previous":'<svg width="7" height="12" viewBox="0 0 7 12"><polyline fill="none" stroke="#000" stroke-width="1.2" points="6 1 1 6 6 11"/></svg>',"search-icon":Fr,"search-large":'<svg width="40" height="40" viewBox="0 0 40 40"><circle fill="none" stroke="#000" stroke-width="1.8" cx="17.5" cy="17.5" r="16.5"/><line fill="none" stroke="#000" stroke-width="1.8" x1="38" y1="39" x2="29" y2="30"/></svg>',"search-navbar":'<svg width="24" height="24" viewBox="0 0 24 24"><circle fill="none" stroke="#000" stroke-width="1.1" cx="10.5" cy="10.5" r="9.5"/><line fill="none" stroke="#000" stroke-width="1.1" x1="23" y1="23" x2="17" y2="17"/></svg>',"search-toggle-icon":Fr,"slidenav-next":'<svg width="14" height="24" viewBox="0 0 14 24"><polyline fill="none" stroke="#000" stroke-width="1.4" points="1.225,23 12.775,12 1.225,1"/></svg>',"slidenav-next-large":'<svg width="25" height="40" viewBox="0 0 25 40"><polyline fill="none" stroke="#000" stroke-width="2" points="4.002,38.547 22.527,20.024 4,1.5"/></svg>',"slidenav-previous":'<svg width="14" height="24" viewBox="0 0 14 24"><polyline fill="none" stroke="#000" stroke-width="1.4" points="12.775,1 1.225,12 12.775,23"/></svg>',"slidenav-previous-large":'<svg width="25" height="40" viewBox="0 0 25 40"><polyline fill="none" stroke="#000" stroke-width="2" points="20.527,1.5 2,20.024 20.525,38.547"/></svg>'},Vr={install:function(t){t.icon.add=(e,i)=>{const n=k(e)?{[e]:i}:e;F(n,((t,e)=>{qr[e]=t,delete na[e]})),t._initialized&&Ce(document.body,(e=>F(t.getComponents(e),(t=>{t.$options.isIcon&&t.icon in n&&t.$reset()}))))}},mixins:[jr],args:"icon",props:{icon:String},isIcon:!0,beforeConnect(){nt(this.$el,"uc-icon")},methods:{async getSvg(){const t=function(t){return t=sa[t]||t,qr[t]?(na[t]||(na[t]=Te((qr[function(t){return Xe?z(z(t,"left","right"),"previous","next"):t}(t)]||qr[t]).trim())),na[t].cloneNode(!0)):null}(this.icon);if(!t)throw"Icon not found.";return t}}},Rr={args:!1,extends:Vr,data:t=>({icon:o(t.constructor.options.name)}),beforeConnect(){nt(this.$el,this.$options.id)}},Ur={extends:Rr,beforeConnect(){const t=this.$props.icon;this.icon=this.$el.closest(".uc-nav-primary")?`${t}-large`:t}},Yr={extends:Rr,mixins:[ms],i18n:{toggle:"Open Search",submit:"Submit Search"},beforeConnect(){const t=at(this.$el,"uc-search-toggle")||at(this.$el,"uc-navbar-toggle");if(this.icon=t?"search-toggle-icon":at(this.$el,"uc-search-icon")&&xt(this.$el,".uc-search-large")?"search-large":xt(this.$el,".uc-search-navbar")?"search-navbar":this.$props.icon,!tt(this.$el,"aria-label"))if(t){const t=this.t("toggle");Q(this.$el,"aria-label",t)}else{const t=this.$el.closest("a,button");if(t){Q(t,"aria-label",this.t("submit"))}}}},Xr={extends:Rr,beforeConnect(){Q(this.$el,"role","status")},methods:{async getSvg(){const t=await Vr.methods.getSvg.call(this);return 1!==this.ratio&&Qt(Te("circle",t),"strokeWidth",1/this.ratio),t}}},Jr={extends:Rr,mixins:[ms],beforeConnect(){const t=this.$el.closest("a,button");Q(t,"role",null!==this.role&&de(t,"a")?"button":this.role);const e=this.t("label");e&&!tt(t,"aria-label")&&Q(t,"aria-label",e)}},Gr={extends:Jr,beforeConnect(){nt(this.$el,"uc-slidenav");const t=this.$props.icon;this.icon=at(this.$el,"uc-slidenav-large")?`${t}-large`:t}},Zr={extends:Jr,i18n:{label:"Open menu"}},Kr={extends:Jr,i18n:{label:"Close"},beforeConnect(){this.icon="close-"+(at(this.$el,"uc-close-large")?"large":"icon")}},Qr={extends:Jr,i18n:{label:"Open"}},ta={extends:Jr,i18n:{label:"Back to top"}},ea={extends:Jr,i18n:{label:"Next page"},data:{role:null}},ia={extends:Jr,i18n:{label:"Previous page"},data:{role:null}},na={};const sa={twitter:"x"};var oa={args:"dataSrc",props:{dataSrc:String,sources:String,margin:String,target:String,loading:String},data:{dataSrc:"",sources:!1,margin:"50%",target:!1,loading:"lazy"},connected(){"lazy"!==this.loading?this.load():ha(this.$el)&&(this.$el.loading="lazy",ra(this.$el))},disconnected(){this.img&&(this.img.onload=""),delete this.img},observe:mn({target:({$el:t,$props:e})=>[t,...Et(e.target,t)],handler(t,e){this.load(),e.disconnect()},options:({margin:t})=>({rootMargin:t}),filter:({loading:t})=>"lazy"===t}),methods:{load(){if(this.img)return this.img;const t=ha(this.$el)?this.$el:function(t,e,i){const n=new Image;return function(t,e){if(e=function(t){if(!t)return[];if(h(t,"["))try{t=JSON.parse(t)}catch{t=[]}else t=cn(t);return f(t)||(t=[t]),t.filter((t=>!E(t)))}(e),e.length){const i=Ie("<picture>");for(const t of e){const e=Ie("<source>");Q(e,t),me(i,e)}me(i,t)}}(n,i),la(t,n),n.onload=()=>{ra(t,n.currentSrc)},Q(n,"src",e),n}(this.$el,this.dataSrc,this.sources);return et(t,"loading"),ra(this.$el,t.currentSrc),this.img=t}}};function ra(t,e){if(ha(t)){const e=vt(t);(de(e,"picture")?St(e):[t]).forEach((t=>la(t,t)))}else e&&!d(t.style.backgroundImage,e)&&(Qt(t,"backgroundImage",`url(${jt(e)})`),Vt(t,Rt("load",!1)))}const aa=["data-src","data-srcset","sizes"];function la(t,e){for(const i of aa){const n=it(t,i);n&&Q(e,i.replace(/^(data-)+/,""),n)}}function ha(t){return de(t,"img")}var ca={props:{target:String,selActive:String},data:{target:!1,selActive:!1},computed:{target:({target:t},e)=>t?Ee(t,e):[e]},observe:[gn({target:({target:t})=>t,options:{attributes:!0,attributeFilter:["class"],attributeOldValue:!0}}),{target:({target:t})=>t,observe:(t,e)=>{const i=ui([...t,document.documentElement],e),n=[Lt(document,"scroll itemshown itemhidden",e,{passive:!0,capture:!0}),Lt(document,"show hide transitionstart",(t=>(e(),i.observe(t.target)))),Lt(document,"shown hidden transitionend transitioncancel",(t=>(e(),i.unobserve(t.target))))];return{disconnect(){i.disconnect(),n.map((t=>t()))}}},handler(){this.$emit()}}],update:{read(){for(const t of this.target)rt(t,"uc-light,uc-dark",!this.selActive||bt(t,this.selActive)?da(t):"")}}};function da(t){const{left:e,top:i,height:n,width:s}=Pe(t);let o;for(const r of[.25,.5,.75]){const a=t.ownerDocument.elementsFromPoint(Math.max(0,e)+s*r,Math.max(0,i)+n/2);for(const e of a){if(t.contains(e)||e.closest('[class*="-leave"]')&&a.some((t=>e!==t&&bt(t,'[class*="-enter"]'))))continue;const i=Qt(e,"--uc-inverse");if(i){if(i===o)return`uc-${i}`;o=i;break}}}return o?`uc-${o}`:""}var ua={mixins:[en,ho],props:{fill:String},data:{fill:"",clsWrapper:"uc-leader-fill",clsHide:"uc-leader-hide",attrFill:"data-fill"},computed:{fill:({fill:t},e)=>t||Qt(e,"--uc-leader-fill-content")},connected(){[this.wrapper]=$e(this.$el,`<span class="${this.clsWrapper}">`)},disconnected(){ye(this.wrapper.childNodes)},observe:pn(),update:{read(){return{width:Math.trunc(this.$el.offsetWidth/2),fill:this.fill,hide:!this.matchMedia}},write({width:t,fill:e,hide:i}){lt(this.wrapper,this.clsHide,i),Q(this.wrapper,this.attrFill,new Array(t).join(e))},events:["resize"]}},fa={install:function({modal:t}){function e(e,i,n=R,s=R){i={bgClose:!1,escClose:!0,...i,i18n:{...t.i18n,...null==i?void 0:i.i18n}};const o=t.dialog(e(i),i);return m(new Promise((t=>{const e=Lt(o.$el,"hide",(()=>t(n())));Lt(o.$el,"submit","form",(i=>{i.preventDefault(),t(s(o)),e(),o.hide()}))})),{dialog:o})}t.dialog=function(e,i){const n=t(`<div class="uc-modal"> <div class="uc-modal-dialog">${e}</div> </div>`,{stack:!0,role:"alertdialog",...i});return n.show(),Lt(n.$el,"hidden",(async()=>{await Promise.resolve(),n.$destroy(!0)}),{self:!0}),n},t.alert=function(t,i){return e((({i18n:e})=>`<div class="uc-modal-body">${k(t)?t:fe(t)}</div> <div class="uc-modal-footer uc-text-right"> <button class="uc-button uc-button-primary uc-modal-close" autofocus>${e.ok}</button> </div>`),i)},t.confirm=function(t,i){return e((({i18n:e})=>`<form> <div class="uc-modal-body">${k(t)?t:fe(t)}</div> <div class="uc-modal-footer uc-text-right"> <button class="uc-button uc-button-default uc-modal-close" type="button">${e.cancel}</button> <button class="uc-button uc-button-primary" autofocus>${e.ok}</button> </div> </form>`),i,(()=>Promise.reject()))},t.prompt=function(t,i,n){const s=e((({i18n:e})=>`<form class="uc-form-stacked"> <div class="uc-modal-body"> <label>${k(t)?t:fe(t)}</label> <input class="uc-input" value="${i||""}" autofocus> </div> <div class="uc-modal-footer uc-text-right"> <button class="uc-button uc-button-default uc-modal-close" type="button">${e.cancel}</button> <button class="uc-button uc-button-primary">${e.ok}</button> </div> </form>`),n,(()=>null),(()=>r.value)),{$el:o}=s.dialog,r=Te("input",o);return Lt(o,"show",(()=>r.select())),s},t.i18n={ok:"Ok",cancel:"Cancel"}},mixins:[os],data:{clsPage:"uc-modal-page",selPanel:".uc-modal-dialog",selClose:".uc-modal-close, .uc-modal-close-default, .uc-modal-close-outside, .uc-modal-close-full"},events:[{name:"fullscreenchange webkitendfullscreen",capture:!0,handler(t){de(t.target,"video")&&this.isToggled()&&!document.fullscreenElement&&this.hide()}},{name:"show",self:!0,handler(){at(this.panel,"uc-margin-auto-vertical")?nt(this.$el,"uc-flex"):Qt(this.$el,"display","block"),Oe(this.$el)}},{name:"hidden",self:!0,handler(){Qt(this.$el,"display",""),st(this.$el,"uc-flex")}}]};var pa={extends:fr,data:{targets:"> .uc-parent",toggle:"> a",content:"> ul"}};const ma="uc-navbar-transparent";var ga={extends:Er,props:{dropbarTransparentMode:Boolean},data:{clsDrop:"uc-navbar-dropdown",selNavItem:".uc-navbar-nav > li > a,a.uc-navbar-item,button.uc-navbar-item,.uc-navbar-item a,.uc-navbar-item button,.uc-navbar-toggle",dropbarTransparentMode:!1},computed:{navbarContainer:(t,e)=>e.closest(".uc-navbar-container")},watch:{items(){const t=at(this.$el,"uc-navbar-justify"),e=Ee(".uc-navbar-nav, .uc-navbar-left, .uc-navbar-right",this.$el);for(const i of e){Qt(i,"flexGrow",t?Ee(".uc-navbar-nav > li > a, .uc-navbar-item, .uc-navbar-toggle",i).length:"")}}},events:[{name:"show",el(){return this.dropContainer},handler({target:t}){"remove"===this.getTransparentMode(t)&&at(this.navbarContainer,ma)&&(st(this.navbarContainer,ma),this._transparent=!0)}},{name:"hide",el(){return this.dropContainer},async handler(){await new Promise((t=>setTimeout(t))),!this.getActive()&&this._transparent&&(nt(this.navbarContainer,ma),this._transparent=null)}}],methods:{getTransparentMode(t){if(!this.navbarContainer)return;if(this.dropbar&&this.isDropbarDrop(t))return this.dropbarTransparentMode;const e=this.getDropdown(t);return e&&at(t,"uc-dropbar")?e.inset?"behind":"remove":void 0},getDropbarOffset(t){const{top:e,height:i}=Me(this.navbarContainer);return e+("behind"===this.dropbarTransparentMode?0:i+t)}}};var va={mixins:[os],args:"mode",props:{mode:String,flip:Boolean,overlay:Boolean,swiping:Boolean},data:{mode:"slide",flip:!1,overlay:!1,clsPage:"uc-offcanvas-page",clsContainer:"uc-offcanvas-container",selPanel:".uc-offcanvas-bar",clsFlip:"uc-offcanvas-flip",clsContainerAnimation:"uc-offcanvas-container-animation",clsSidebarAnimation:"uc-offcanvas-bar-animation",clsMode:"uc-offcanvas",clsOverlay:"uc-offcanvas-overlay",selClose:".uc-offcanvas-close",container:!1,swiping:!0},computed:{clsFlip:({flip:t,clsFlip:e})=>t?e:"",clsOverlay:({overlay:t,clsOverlay:e})=>t?e:"",clsMode:({mode:t,clsMode:e})=>`${e}-${t}`,clsSidebarAnimation:({mode:t,clsSidebarAnimation:e})=>"none"===t||"reveal"===t?"":e,clsContainerAnimation:({mode:t,clsContainerAnimation:e})=>"push"!==t&&"reveal"!==t?"":e,transitionElement({mode:t}){return"reveal"===t?vt(this.panel):this.panel}},observe:xn({filter:({swiping:t})=>t}),update:{read(){this.isToggled()&&!ut(this.$el)&&this.hide()},events:["resize"]},events:[{name:"touchmove",self:!0,passive:!1,filter(){return this.overlay},handler(t){t.cancelable&&t.preventDefault()}},{name:"show",self:!0,handler(){"reveal"===this.mode&&!at(vt(this.panel),this.clsMode)&&(xe(this.panel,"<div>"),nt(vt(this.panel),this.clsMode));const{body:t,scrollingElement:e}=document;nt(t,this.clsContainer,this.clsFlip),Qt(t,"touch-action","pan-y pinch-zoom"),Qt(this.$el,"display","block"),Qt(this.panel,"maxWidth",e.clientWidth),nt(this.$el,this.clsOverlay),nt(this.panel,this.clsSidebarAnimation,"reveal"===this.mode?"":this.clsMode),Oe(t),nt(t,this.clsContainerAnimation),this.clsContainerAnimation&&(wa().content+=",user-scalable=0")}},{name:"hide",self:!0,handler(){st(document.body,this.clsContainerAnimation),Qt(document.body,"touch-action","")}},{name:"hidden",self:!0,handler(){this.clsContainerAnimation&&function(){const t=wa();t.content=t.content.replace(/,user-scalable=0$/,"")}(),"reveal"===this.mode&&ye(this.panel),st(this.panel,this.clsSidebarAnimation,this.clsMode),st(this.$el,this.clsOverlay),Qt(this.$el,"display",""),Qt(this.panel,"maxWidth",""),st(document.body,this.clsContainer,this.clsFlip)}},{name:"swipeLeft swipeRight",handler(t){this.isToggled()&&c(t.type,"Left")^this.flip&&this.hide()}}]};function wa(){return Te('meta[name="viewport"]',document.head)||me(document.head,'<meta name="viewport">')}var ba={mixins:[en],props:{selContainer:String,selContent:String,minHeight:Number},data:{selContainer:".uc-modal",selContent:".uc-modal-dialog",minHeight:150},computed:{container:({selContainer:t},e)=>e.closest(t),content:({selContent:t},e)=>e.closest(t)},observe:pn({target:({container:t,content:e})=>[t,e]}),update:{read(){return!!(this.content&&this.container&&ut(this.$el))&&{max:Math.max(this.minHeight,Oe(this.container)-(Pe(this.content).height-Oe(this.$el)))}},write({max:t}){Qt(this.$el,{minHeight:this.minHeight,maxHeight:t})},events:["resize"]}},xa={props:["width","height"],connected(){nt(this.$el,"uc-responsive-width"),Qt(this.$el,"aspectRatio",`${this.width}/${this.height}`)}},$a={props:{offset:Number},data:{offset:0},connected(){!function(t){ya.size||Lt(document,"click",Sa),ya.add(t)}(this)},disconnected(){!function(t){ya.delete(t),ya.size||Wt(document,"click",Sa)}(this)},methods:{async scrollTo(t){t=t&&Te(t)||document.body,Vt(this.$el,"beforescroll",[this,t])&&(await Ei(t,{offset:this.offset}),Vt(this.$el,"scrolled",[this,t]))}}};const ya=new Set;function Sa(t){if(!t.defaultPrevented)for(const e of ya)e.$el.contains(t.target)&&kt(e.$el)&&(t.preventDefault(),window.location.href!==e.$el.href&&window.history.pushState({},"",e.$el.href),e.scrollTo(Ct(e.$el)))}var Ia={args:"cls",props:{cls:String,target:String,hidden:Boolean,margin:String,repeat:Boolean,delay:Number},data:()=>({cls:"",target:!1,hidden:!0,margin:"-1px",repeat:!1,delay:0,inViewClass:"uc-scrollspy-inview"}),computed:{elements:({target:t},e)=>t?Ee(t,e):[e]},watch:{elements(t){this.hidden&&Qt(wt(t,`:not(.${this.inViewClass})`),"opacity",0)}},connected(){this.elementData=new Map},disconnected(){for(const[t,e]of this.elementData.entries())st(t,this.inViewClass,(null==e?void 0:e.cls)||"");delete this.elementData},observe:mn({target:({elements:t})=>t,handler(t){const e=this.elementData;for(const{target:i,isIntersecting:n}of t){e.has(i)||e.set(i,{cls:it(i,"uc-scrollspy-class")||this.cls});const t=e.get(i);!this.repeat&&t.show||(t.show=n)}this.$emit()},options:({margin:t})=>({rootMargin:t}),args:{intersecting:!1}}),update:[{write(t){for(const[e,i]of this.elementData.entries())!i.show||i.inview||i.queued?!i.show&&i.inview&&!i.queued&&this.repeat&&this.toggle(e,!1):(i.queued=!0,t.promise=(t.promise||Promise.resolve()).then((()=>new Promise((t=>setTimeout(t,this.delay))))).then((()=>{this.toggle(e,!0),setTimeout((()=>{i.queued=!1,this.$emit()}),300)})))}}],methods:{toggle(t,e){var i;const n=this.elementData.get(t);if(n){if(null==(i=n.off)||i.call(n),Qt(t,"opacity",!e&&this.hidden?0:""),lt(t,this.inViewClass,e),lt(t,n.cls),/\buc-animation-/.test(n.cls)){const i=()=>ot(t,"uc-animation-[\\w-]+");e?n.off=qt(t,"animationcancel animationend",i,{self:!0}):i()}Vt(t,e?"inview":"outview"),n.inview=e,this.$update(t)}}}},ka={props:{cls:String,closest:Boolean,scroll:Boolean,overflow:Boolean,offset:Number},data:{cls:"uc-active",closest:!1,scroll:!1,overflow:!0,offset:0},computed:{links:(t,e)=>Ee('a[href*="#"]',e).filter((t=>t.hash&&kt(t))),elements({closest:t}){return this.links.map((e=>e.closest(t||"*")))}},watch:{links(t){this.scroll&&this.$create("scroll",t,{offset:this.offset})}},observe:[mn(),bn()],update:[{read(){const t=this.links.map(Ct).filter(Boolean),{length:e}=t;if(!e||!ut(this.$el))return!1;const i=Pi(t,!0),{scrollTop:n,scrollHeight:s}=i,o=_i(i);let r=!1;if(n===s-o.height)r=e-1;else{for(let e=0;e<t.length;e++){const i=Bi(t[e]),n=this.offset+(i?Me(i).height:0);if(Me(t[e]).top-o.top-n>0)break;r=+e}!1===r&&this.overflow&&(r=0)}return{active:r}},write({active:t}){const e=!1!==t&&!at(this.elements[t],this.cls);this.links.forEach((t=>t.blur()));for(let e=0;e<this.elements.length;e++)lt(this.elements[e],this.cls,+e===t);e&&Vt(this.$el,"active",[t,this.elements[t]])},events:["scroll","resize"]}]},Ca={mixins:[en,ho],props:{position:String,top:null,bottom:null,start:null,end:null,offset:String,overflowFlip:Boolean,animation:String,clsActive:String,clsInactive:String,clsFixed:String,clsBelow:String,selTarget:String,showOnUp:Boolean,targetOffset:Number},data:{position:"top",top:!1,bottom:!1,start:!1,end:!1,offset:0,overflowFlip:!1,animation:"",clsActive:"uc-active",clsInactive:"",clsFixed:"uc-sticky-fixed",clsBelow:"uc-sticky-below",selTarget:"",showOnUp:!1,targetOffset:!1},computed:{target:({selTarget:t},e)=>t&&Te(t,e)||e},connected(){this.start=Ea(this.start||this.top),this.end=Ea(this.end||this.bottom),this.placeholder=Te("+ .uc-sticky-placeholder",this.$el)||Te('<div class="uc-sticky-placeholder"></div>'),this.isFixed=!1,this.setActive(!1)},beforeDisconnect(){this.isFixed&&(this.hide(),st(this.target,this.clsInactive)),Aa(this.$el),be(this.placeholder),this.placeholder=null},observe:[wn({handler(){je("100vh","height")!==this._data.viewport&&this.$emit("resize")}}),bn({target:()=>document.scrollingElement}),pn({target:()=>document.scrollingElement,options:{box:"content-box"}}),pn({target:({$el:t})=>t})],events:[{name:"load hashchange popstate",el:()=>window,filter(){return!1!==this.targetOffset},handler(){const{scrollingElement:t}=document;!location.hash||0===t.scrollTop||setTimeout((()=>{const e=Me(Te(location.hash)),i=Me(this.$el);this.isFixed&&U(e,i)&&(t.scrollTop=e.top-i.height-je(this.targetOffset,"height",this.placeholder)-je(this.offset,"height",this.placeholder))}))}},{name:"transitionstart",handler(){this.transitionInProgress=qt(this.$el,"transitionend transitioncancel",(()=>this.transitionInProgress=null))}}],update:[{read({height:t,width:e,margin:i,sticky:n}){if(this.inactive=!this.matchMedia||!ut(this.$el),this.inactive)return;const s=this.isFixed&&!this.transitionInProgress;s&&(Da(this.target),this.hide()),this.active||(({height:t,width:e}=Me(this.$el)),i=Qt(this.$el,"margin")),s&&this.show();const o=je("100vh","height"),r=Oe(window),a=Math.max(0,document.scrollingElement.scrollHeight-o);let l=this.position;this.overflowFlip&&t>o&&(l="top"===l?"bottom":"top");const h=this.isFixed?this.placeholder:this.$el;let c=je(this.offset,"height",n?this.$el:h);"bottom"===l&&(t<r||this.overflowFlip)&&(c+=r-t);const d=this.overflowFlip?0:Math.max(0,t+c-o),u=Me(h).top,f=Me(this.$el).height,p=(!1===this.start?u:Ta(this.start,this.$el,u))-c,m=!1===this.end?a:Math.min(a,Ta(this.end,this.$el,u+t,!0)-f-c+d);return n=a&&!this.showOnUp&&p+c===u&&m===Math.min(a,Ta(!0,this.$el,0,!0)-f-c+d)&&"visible"===Qt(vt(this.$el),"overflowY"),{start:p,end:m,offset:c,overflow:d,height:t,elHeight:f,width:e,margin:i,top:Be(h)[0],sticky:n,viewport:o}},write({height:t,width:e,margin:i,offset:n,sticky:s}){if((this.inactive||s||!this.isFixed)&&Aa(this.$el),this.inactive)return;s&&(t=e=i=0,Qt(this.$el,{position:"sticky",top:n}));const{placeholder:o}=this;Qt(o,{height:t,width:e,margin:i}),(vt(o)!==vt(this.$el)||s^It(o)<It(this.$el))&&((s?ge:ve)(this.$el,o),o.hidden=!0)},events:["resize"]},{read({scroll:t=0,dir:e="down",overflow:i,overflowScroll:n=0,start:s,end:o,elHeight:r,height:a,sticky:l}){const h=document.scrollingElement.scrollTop,c=t<=h?"down":"up",d=this.isFixed?this.placeholder:this.$el;return{dir:c,prevDir:e,scroll:h,prevScroll:t,below:h>Me(d).top+(l?Math.min(a,r):a),offsetParentTop:Me(d.offsetParent).top,overflowScroll:V(n+V(h,s,o)-V(t,s,o),0,i)}},write(t,e){const i=e.has("scroll"),{initTimestamp:n=0,dir:s,prevDir:o,scroll:r,prevScroll:a=0,top:l,start:h,below:c}=t;if(r<0||r===a&&i||this.showOnUp&&!i&&!this.isFixed)return;const d=Date.now();if((d-n>300||s!==o)&&(t.initScroll=r,t.initTimestamp=d),!(this.showOnUp&&!this.isFixed&&Math.abs(t.initScroll-r)<=30&&Math.abs(a-r)<=10))if(this.inactive||r<h||this.showOnUp&&(r<=h||"down"===s&&i||"up"===s&&!this.isFixed&&!c)){if(!this.isFixed)return void(ce.inProgress(this.$el)&&l>r&&(ce.cancel(this.$el),this.hide()));if(this.animation&&c){if(at(this.$el,"uc-animation-leave"))return;ce.out(this.$el,this.animation).then((()=>this.hide()),R)}else this.hide()}else this.isFixed?this.update():this.animation&&c?(this.show(),ce.in(this.$el,this.animation).catch(R)):(Da(this.target),this.show())},events:["resize","resizeViewport","scroll"]}],methods:{show(){this.isFixed=!0,this.update(),this.placeholder.hidden=!1},hide(){const{offset:t,sticky:e}=this._data;this.setActive(!1),st(this.$el,this.clsFixed,this.clsBelow),e?Qt(this.$el,"top",t):Qt(this.$el,{position:"",top:"",width:"",marginTop:""}),this.placeholder.hidden=!0,this.isFixed=!1},update(){let{width:t,scroll:e=0,overflow:i,overflowScroll:n=0,start:s,end:o,offset:r,offsetParentTop:a,sticky:l,below:h}=this._data;const c=0!==s||e>s;if(!l){let s="fixed";e>o&&(r+=o-a+n-i,s="absolute"),Qt(this.$el,{position:s,width:t,marginTop:0},"important")}Qt(this.$el,"top",r-n),this.setActive(c),lt(this.$el,this.clsBelow,h),nt(this.$el,this.clsFixed)},setActive(t){const e=this.active;this.active=t,t?(rt(this.target,this.clsInactive,this.clsActive),e!==t&&Vt(this.$el,"active")):(rt(this.target,this.clsActive,this.clsInactive),e!==t&&(Da(this.target),Vt(this.$el,"inactive")))}}};function Ta(t,e,i,n){if(!t)return 0;if(T(t)||k(t)&&t.match(/^-?\d/))return i+je(t,"height",e,!0);{const i=!0===t?vt(e):Tt(t,e);return Me(i).bottom-(n&&null!=i&&i.contains(e)?M(Qt(i,"paddingBottom")):0)}}function Ea(t){return"true"===t||"false"!==t&&t}function Aa(t){Qt(t,{position:"",top:"",marginTop:"",width:""})}function Da(t){nt(t,"uc-transition-disable"),requestAnimationFrame((()=>st(t,"uc-transition-disable")))}var Pa={mixins:[jr],args:"src",props:{src:String,icon:String,attributes:"list",strokeAnimation:Boolean},data:{strokeAnimation:!1},observe:[gn({async handler(){const t=await this.svg;t&&Ma.call(this,t)},options:{attributes:!0,attributeFilter:["id","class","style"]}})],async connected(){d(this.src,"#")&&([this.src,this.icon]=this.src.split("#"));const t=await this.svg;t&&(Ma.call(this,t),this.strokeAnimation&&function(t){const e=uo(t);e&&Qt(t,"--uc-animation-stroke",e)}(t))},methods:{async getSvg(){return de(this.$el,"img")&&!this.$el.complete&&"lazy"===this.$el.loading&&await new Promise((t=>qt(this.$el,"load",t))),function(t,e){return e&&d(t,"<symbol")&&(t=Oa(t)[e]||t),t=Te(t.substr(t.indexOf("<svg"))),(null==t?void 0:t.hasChildNodes())&&t}(await _a(this.src),this.icon)||Promise.reject("SVG not found.")}}};function Ma(t){const{$el:e}=this;nt(t,Q(e,"class"),"uc-svg");for(let i=0;i<e.style.length;i++){const n=e.style[i];Qt(t,n,Qt(e,n))}for(const e in this.attributes){const[i,n]=this.attributes[e].split(":",2);Q(t,i,n)}this.$el.id||et(t,"id")}const _a=K((async t=>t?h(t,"data:")?decodeURIComponent(t.split(",")[1]):(await fetch(t)).text():Promise.reject()));const Ba=/<symbol([^]*?id=(['"])(.+?)\2[^]*?<\/)symbol>/g,Oa=K((function(t){const e={};let i;for(Ba.lastIndex=0;i=Ba.exec(t);)e[i[3]]=`<svg ${i[1]}svg>`;return e}));const Na=".uc-disabled *, .uc-disabled, [disabled]";var za={mixins:[ts],args:"connect",props:{connect:String,toggle:String,itemNav:String,active:Number,followFocus:Boolean,swiping:Boolean},data:{connect:"~.uc-switcher",toggle:"> * > :first-child",itemNav:!1,active:0,cls:"uc-active",attrItem:"uc-switcher-item",selVertical:".uc-nav",followFocus:!1,swiping:!0},computed:{connects:({connect:t},e)=>Et(t,e),connectChildren(){return this.connects.map((t=>St(t))).flat()},toggles:({toggle:t},e)=>Ee(t,e),children(t,e){return St(e).filter((t=>this.toggles.some((e=>t.contains(e)))))}},watch:{connects(t){this.swiping&&Qt(t,"touchAction","pan-y pinch-zoom"),this.$emit()},connectChildren(){let t=Math.max(0,this.index());for(const e of this.connects)St(e).forEach(((e,i)=>lt(e,this.cls,i===t)));this.$emit()},toggles(t){this.$emit();const e=this.index();this.show(~e?e:t[this.active]||t[0])}},connected(){Q(this.$el,"role","tablist")},observe:[vn({targets:({connectChildren:t})=>t}),xn({target:({connects:t})=>t,filter:({swiping:t})=>t})],events:[{name:"click keydown",delegate(){return this.toggle},handler(t){!bt(t.current,Na)&&("click"===t.type||t.keyCode===Fn)&&(t.preventDefault(),this.show(t.current))}},{name:"keydown",delegate(){return this.toggle},handler(t){const{current:e,keyCode:i}=t,n=bt(this.$el,this.selVertical);let s=i===Ln?0:i===jn?"last":i===Wn&&!n||i===qn&&n?"previous":i===Vn&&!n||i===Rn&&n?"next":-1;if(~s){t.preventDefault();const i=this.toggles.filter((t=>!bt(t,Na))),n=i[Z(s,i,i.indexOf(e))];n.focus(),this.followFocus&&this.show(n)}}},{name:"click",el(){return this.connects.concat(this.itemNav?Et(this.itemNav,this.$el):[])},delegate(){return`[${this.attrItem}],[data-${this.attrItem}]`},handler(t){t.target.closest("a,button")&&(t.preventDefault(),this.show(it(t.current,this.attrItem)))}},{name:"swipeRight swipeLeft",filter(){return this.swiping},el(){return this.connects},handler({type:t}){this.show(c(t,"Left")?"next":"previous")}}],update(){var t;Q(this.connects,"role","presentation"),Q(St(this.$el),"role","presentation");for(const e in this.toggles){const i=this.toggles[e],n=null==(t=this.connects[0])?void 0:t.children[e];Q(i,"role","tab"),n&&(i.id=Ks(this,i),n.id=Ks(this,n),Q(i,"aria-controls",n.id),Q(n,{role:"tabpanel","aria-labelledby":i.id}))}Q(this.$el,"aria-orientation",bt(this.$el,this.selVertical)?"vertical":null)},methods:{index(){return u(this.children,(t=>at(t,this.cls)))},show(t){const e=this.toggles.filter((t=>!bt(t,Na))),i=this.index(),n=Z(!$(t)||d(e,t)?t:0,e,Z(this.toggles[i],e)),s=Z(e[n],this.toggles);this.children.forEach(((t,e)=>{lt(t,this.cls,s===e),Q(this.toggles[e],{"aria-selected":s===e,tabindex:s===e?null:-1})}));const o=i>=0&&i!==n;this.connects.forEach((async({children:t})=>{const e=p(t).filter(((t,e)=>e!==s&&at(t,this.cls)));await this.toggleElement(e,!1,o),await this.toggleElement(t[s],!0,o)}))}}},Ha={mixins:[en],extends:za,props:{media:Boolean},data:{media:960,attrItem:"uc-tab-item",selVertical:".uc-tab-left,.uc-tab-right"},connected(){const t=at(this.$el,"uc-tab-left")?"uc-tab-left":!!at(this.$el,"uc-tab-right")&&"uc-tab-right";t&&this.$create("toggle",this.$el,{cls:t,mode:"media",media:this.media})}};var Fa={mixins:[ho,ts],args:"target",props:{href:String,target:null,mode:"list",queued:Boolean},data:{href:!1,target:!1,mode:"click",queued:!0},computed:{target:({target:t},e)=>(t=Et(t||e.hash,e)).length?t:[e]},connected(){d(this.mode,"media")||(gt(this.$el)||Q(this.$el,"tabindex","0"),!this.cls&&de(this.$el,"a")&&Q(this.$el,"role","button"))},observe:vn({target:({target:t})=>t}),events:[{name:Ze,filter(){return d(this.mode,"hover")},handler(t){this._preventClick=null,Gt(t)&&!I(this._showState)&&!this.$el.disabled&&(Vt(this.$el,"focus"),qt(document,Ze,(()=>Vt(this.$el,"blur")),!0,(t=>!this.$el.contains(t.target))),d(this.mode,"click")&&(this._preventClick=!0))}},{name:`mouseenter mouseleave ${ti} ${ei} focus blur`,filter(){return d(this.mode,"hover")},handler(t){if(Gt(t)||this.$el.disabled)return;const e=d(["mouseenter",ti,"focus"],t.type),i=this.isToggled(this.target);e||!(!I(this._showState)||"blur"!==t.type&&bt(this.$el,":focus")||"blur"===t.type&&bt(this.$el,":hover"))?e&&I(this._showState)&&i!==this._showState||(this._showState=e?i:null,this.toggle("toggle"+(e?"show":"hide"))):i===this._showState&&(this._showState=null)}},{name:"keydown",filter(){return d(this.mode,"click")&&!de(this.$el,"input")},handler(t){32===t.keyCode&&(t.preventDefault(),this.$el.click())}},{name:"click",filter(){return["click","hover"].some((t=>d(this.mode,t)))},handler(t){let e;(this._preventClick||t.target.closest('a[href="#"], a[href=""]')||(e=t.target.closest("a[href]"))&&(!this.isToggled(this.target)||e.hash&&bt(this.target,e.hash)))&&t.preventDefault(),!this._preventClick&&d(this.mode,"click")&&this.toggle()}},{name:"mediachange",filter(){return d(this.mode,"media")},el(){return this.target},handler(t,e){e.matches^this.isToggled(this.target)&&this.toggle()}}],methods:{async toggle(t){if(!Vt(this.target,t||"toggle",[this]))return;if(tt(this.$el,"aria-expanded")&&Q(this.$el,"aria-expanded",!this.isToggled(this.target)),!this.queued)return this.toggleElement(this.target);const e=this.target.filter((t=>at(t,this.clsLeave)));if(e.length){for(const t of this.target){const i=d(e,t);this.toggleElement(t,i,i)}return}const i=this.target.filter(this.isToggled);await this.toggleElement(i,!1)&&await this.toggleElement(this.target.filter((t=>!d(i,t))),!0)}}};return F(Object.freeze({__proto__:null,Accordion:fr,Alert:mr,Close:Kr,Cover:wr,Drop:yr,DropParentIcon:Rr,Dropdown:yr,Dropnav:Er,FormCustom:Dr,Grid:Pr,HeightMatch:Br,HeightPlaceholder:zr,HeightViewport:Hr,Icon:Vr,Img:oa,Inverse:ca,Leader:ua,Margin:Sn,Marker:Qr,Modal:fa,Nav:pa,NavParentIcon:Ur,Navbar:ga,NavbarParentIcon:Rr,NavbarToggleIcon:Zr,Offcanvas:va,OverflowAuto:ba,OverlayIcon:Rr,PaginationNext:ea,PaginationPrevious:ia,Responsive:xa,Scroll:$a,Scrollspy:Ia,ScrollspyNav:ka,SearchIcon:Yr,SlidenavNext:Gr,SlidenavPrevious:Gr,Spinner:Xr,Sticky:Ca,Svg:Pa,Switcher:za,Tab:Ha,Toggle:Fa,Totop:ta,Video:vr}),((t,e)=>Ls.component(e,t))),function(t){Ye&&window.MutationObserver&&(document.body?requestAnimationFrame((()=>ar(t))):new MutationObserver(((e,i)=>{document.body&&(ar(t),i.disconnect())})).observe(document.documentElement,{childList:!0}))}(Ls),F(rr,((t,e)=>Ls.component(e,t))),Ls}));