import 'dart:convert';

class MockService {
  static Map<String, dynamic> getMockNewsData() {
    return {
      "status": "ok",
      "articles": [
        {
          "title": "Test Article 1",
          "description": "This is a test article description",
          "urlToImage": "https://via.placeholder.com/300",
          "publishedAt": "2023-06-01T12:00:00Z",
          "content": "This is test content for article 1"
        },
        {
          "title": "Test Article 2",
          "description": "This is another test article description",
          "urlToImage": "https://via.placeholder.com/300",
          "publishedAt": "2023-06-02T12:00:00Z",
          "content": "This is test content for article 2"
        }
      ]
    };
  }
  
  // Add more mock methods as needed
}