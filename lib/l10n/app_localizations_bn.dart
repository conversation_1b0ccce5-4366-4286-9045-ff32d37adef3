// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Bengali Bangla (`bn`).
class AppLocalizationsBn extends AppLocalizations {
  AppLocalizationsBn([String locale = 'bn']) : super(locale);

  @override
  String get appName => 'নিউজ হান্ট';

  @override
  String get popularNews => 'জনপ্রিয় সংবাদ';

  @override
  String get recommendation => 'সুপারিশ';

  @override
  String get channelsYouMayLike => 'আপনি পছন্দ করতে পারেন এমন চ্যানেলগুলি';

  @override
  String get seeAll => 'সব দেখুন';

  @override
  String get forYou => 'আপনার জন্য';

  @override
  String get homeTitle => 'হোম';

  @override
  String get categoryLable => 'বিভাগ';

  @override
  String get discoverTitle => 'আবিষ্কার করুন';

  @override
  String get bookmarkTitle => 'বুকমার্ক';

  @override
  String get collectionOfNoteworthyReads => 'উল্লেখযোগ্য পাঠের সংগ্রহ';

  @override
  String get profile => 'প্রোফাইল';

  @override
  String get generalSettings => 'সাধারণ সেটিংস';

  @override
  String get bookmarks => 'বুকমার্কস';

  @override
  String get theme => 'থিম';

  @override
  String get you => 'আপনি';

  @override
  String get shareApp => 'অ্যাপ শেয়ার করুন';

  @override
  String get stories => 'গল্প';

  @override
  String get followedChannelsPost => 'অনুসরণ করা চ্যানেলের পোস্ট';

  @override
  String get noFollowedChannels => 'কোনো অনুসরণ করা চ্যানেল নেই';

  @override
  String get nameEmailMobileNumberCannotEmpty =>
      'নাম, ইমেল, এবং মোবাইল নম্বর খালি রাখা যাবে না।';

  @override
  String get topicsStories => 'বিষয়বস্তু গল্প';

  @override
  String get contactUs => 'যোগাযোগ করুন';

  @override
  String get language => 'অ্যাপ ভাষা';

  @override
  String get rateApp => 'অ্যাপ রেট দিন';

  @override
  String get privacyPolicy => 'গোপনীয়তা নীতি';

  @override
  String get getStarted => 'শুরু করুন';

  @override
  String get unlockPremiumExperience =>
      'আমাদের নমনীয় সদস্যতা বিকল্পগুলির সাথে আপনার প্রয়োজন অনুযায়ী একটি প্রিমিয়াম অভিজ্ঞতা আনলক করুন';

  @override
  String get selectYourPlan => 'আপনার সদস্যতা পরিকল্পনা নির্বাচন করুন';

  @override
  String get joinMillions => 'মিলিয়ন প্রিমিয়াম ব্যবহারকারীদের সাথে যোগ দিন';

  @override
  String get elevateExperience => 'আপনার অভিজ্ঞতা উন্নত করুন';

  @override
  String get includedBenefits => 'অন্তর্ভুক্ত সুবিধাসমূহ';

  @override
  String get selectPaymentMethod => 'পেমেন্ট পদ্ধতি নির্বাচন করুন';

  @override
  String get choosePaymentOption =>
      'আপনার পছন্দের পেমেন্ট বিকল্প নির্বাচন করুন';

  @override
  String get fullAccessFor => 'পূর্ণ অ্যাক্সেসের জন্য';

  @override
  String get razorpay => 'রেজরপে';

  @override
  String get razorpayDescription =>
      'ক্রেডিট/ডেবিট কার্ড, ইউপিআই ইত্যাদি ব্যবহার করে পেমেন্ট করুন।';

  @override
  String get stripe => 'স্ট্রাইপ';

  @override
  String get stripeDescription => 'আন্তর্জাতিক কার্ড ব্যবহার করে পেমেন্ট করুন';

  @override
  String get aboutUs => 'আমাদের সম্পর্কে';

  @override
  String get searchNews => 'সংবাদ অনুসন্ধান করুন';

  @override
  String get like => 'পছন্দ';

  @override
  String get love => 'ভালোবাসা';

  @override
  String get haha => 'হাসি';

  @override
  String get wow => 'বিস্ময়';

  @override
  String get sad => 'দুঃখিত';

  @override
  String get angry => 'রাগান্বিত';

  @override
  String get liked => 'পছন্দ করা হয়েছে';

  @override
  String get commentOnPost => 'পোস্টে মন্তব্য করুন';

  @override
  String get yourNotificationFeedisQuiet => 'আপনার বিজ্ঞপ্তি ফিড শান্ত';

  @override
  String get checkNotificationChannels =>
      'আপনার প্রিয় চ্যানেলগুলির সর্বশেষ বিজ্ঞপ্তি আপডেটের জন্য পরে আবার দেখুন।';

  @override
  String get goToHomeFeed => 'হোম ফিডে যান';

  @override
  String get chooseTheme => 'থিম নির্বাচন করুন';

  @override
  String get systemMode => 'সিস্টেম মোড';

  @override
  String get darkMode => 'ডার্ক মোড';

  @override
  String get lightMode => 'লাইট মোড';

  @override
  String get apply => 'প্রয়োগ করুন';

  @override
  String get chooseLanguage => 'ভাষা নির্বাচন করুন';

  @override
  String get logout => 'লগ আউট';

  @override
  String get login => 'লগ ইন';

  @override
  String get loginLabel => 'লগইন';

  @override
  String get notification => 'বিজ্ঞপ্তি';

  @override
  String get logOut => 'লগ আউট';

  @override
  String get followedChannels => 'অনুসরণ করা চ্যানেল';

  @override
  String get bookmarkPost => 'পোস্ট বুকমার্ক করুন';

  @override
  String get share => 'শেয়ার করুন';

  @override
  String get logInWithApple => 'অ্যাপল দিয়ে লগ ইন করুন';

  @override
  String get removeBookmark => 'বুকমার্ক সরান';

  @override
  String get comments => 'মন্তব্য';

  @override
  String get view => 'দেখুন';

  @override
  String get addComments => 'মন্তব্য যোগ করুন';

  @override
  String get channels => 'চ্যানেল';

  @override
  String get youNeedToLogInToViewThisPage =>
      'এই পৃষ্ঠা দেখতে আপনাকে লগ ইন করতে হবে।';

  @override
  String get signInToNewsHunt => 'নিউজ হান্টে সাইন ইন করুন';

  @override
  String get logInWithGoogle => 'গুগল দিয়ে লগ ইন করুন';

  @override
  String get logInWithNumber => 'নম্বর দিয়ে লগ ইন করুন';

  @override
  String get emial => 'ইমেল';

  @override
  String get password => 'পাসওয়ার্ড';

  @override
  String get forgotPassword => 'পাসওয়ার্ড ভুলে গেছেন';

  @override
  String get noAccountCreateOne => 'কোনো অ্যাকাউন্ট নেই?';

  @override
  String get createOne => 'একটি তৈরি করুন';

  @override
  String get welcomeBack => 'ফিরে স্বাগতম!';

  @override
  String get pleaseEnterYourNameAndPhoneNumberToLogIn =>
      'লগ ইন করতে আপনার নাম এবং ফোন নম্বর লিখুন';

  @override
  String get phoneNumber => 'ফোন নম্বর';

  @override
  String get skip => 'এড়িয়ে যান';

  @override
  String get termsAndConditions => 'শর্তাবলী';

  @override
  String get tapFor => 'ট্যাপ করুন';

  @override
  String get more => '& আরও';

  @override
  String get or => 'অথবা';

  @override
  String get otpVerification => 'ওটিপি যাচাইকরণ';

  @override
  String get resendCode => 'কোড পুনরায় পাঠান';

  @override
  String get resendCodeIn => 'কোড পুনরায় পাঠান';

  @override
  String get continueLabel => 'চালিয়ে যান';

  @override
  String get name => 'নাম';

  @override
  String get reEnterPassword => 'পাসওয়ার্ড পুনরায় লিখুন';

  @override
  String get createAccount => 'অ্যাকাউন্ট তৈরি করুন';

  @override
  String get withLogin => 'লগইন সহ';

  @override
  String get pleaseEnterYourPhoneNumber => 'আপনার ফোন নম্বর লিখুন';

  @override
  String get noBookmarksfound => 'কোনো বুকমার্ক পাওয়া যায়নি';

  @override
  String get readMore => 'আরও পড়ুন';

  @override
  String get loginRequired => 'লগইন প্রয়োজন';

  @override
  String get needToLogInToSavePost =>
      'এই পোস্ট সংরক্ষণ করতে আপনাকে লগ ইন করতে হবে। আপনি কি এখন লগ ইন করতে চান?';

  @override
  String get youNeedToLoginToUseThisFeature =>
      'এই বৈশিষ্ট্য ব্যবহার করতে আপনাকে লগ ইন করতে হবে।';

  @override
  String get noResultsFound => 'কোনো ফলাফল পাওয়া যায়নি';

  @override
  String get error => 'ত্রুটি';

  @override
  String get pleaseLoginToFollow => 'অনুসরণ করতে লগ ইন করুন';

  @override
  String get follow => 'অনুসরণ করুন';

  @override
  String get following => 'অনুসরণ করছেন';

  @override
  String get entertheverificationcodewejustsentonyouremailaddress =>
      'Enter the verification code we just sent on your email address';

  @override
  String get verifyCode => 'Verify Code';

  @override
  String get resetPasswordEmailSentSuccessfullyPleaseCheckYourEmail =>
      'Reset password email sent successfully. Please check your email';

  @override
  String get pleaseEnterTheEmailAddressLinkedWithYourAccount =>
      'Please enter the email address linked with your account';

  @override
  String get pleaseEnterYourEmail => 'Please enter your email';

  @override
  String get sendEmail => 'Send Email';

  @override
  String get failedToloadUserData => 'Failed to load user data';

  @override
  String get selectImageSource => 'Select Image Source';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get cropImage => 'Crop Image';

  @override
  String get noChangestoNameandImage => 'No Changes to Name and Image';

  @override
  String get profileUpdatedSuccessfully => 'Profile updated successfully';

  @override
  String get failedToSaveupdatedProfile => 'Failed to save updated profile';

  @override
  String get mobileNumber => 'Mobile Number';

  @override
  String get updateProfile => 'Update Profile';

  @override
  String get postingComment => 'Posting comment...';

  @override
  String get playbackSpeed => 'Playback Speed';

  @override
  String get normal => 'Normal';

  @override
  String get replyingTo => 'Replying to';

  @override
  String get commentlabel => 'comment';

  @override
  String get reply => 'Reply';

  @override
  String get viewReply => 'View Reply';

  @override
  String get viewMore => 'View More';

  @override
  String get noCommentsAvailable => 'No comments available.';

  @override
  String get noDataAvailable => 'No data available';

  @override
  String get newsLabel => 'News';

  @override
  String get followers => 'Followers';

  @override
  String get nodatafoundforthissection => 'No data found for this section';

  @override
  String get pleaseCheckYourMobileDataOrWifiConnection =>
      'Please Check your mobile data or wifi connection';

  @override
  String get noInternet => 'ইন্টারনেট সংযোগ নেই';

  @override
  String get noBookMarkFOund => 'No Bookmarks found';

  @override
  String get okLabel => 'ok';

  @override
  String get authenticationFailed => 'Authentication Failed';

  @override
  String get newsBy => 'News by';

  @override
  String get sortBy => 'Sort by';

  @override
  String get by => 'by';

  @override
  String get all => 'All';

  @override
  String get relatedPosts => 'Related Posts';

  @override
  String get noChannelsAvailable => 'No channels available';

  @override
  String get newest => 'Newest';

  @override
  String get mostViewed => 'Most Viewed';

  @override
  String get mostRead => 'Most Read';

  @override
  String get noTopicsAvailable => 'No topics available';

  @override
  String get underMaintenance => 'Under Maintenance';

  @override
  String get sorryTheappisundermaintenance =>
      'Sorry, the app is under maintenance';

  @override
  String get failedToPickImage => 'Failed to pick image';

  @override
  String get noChangesToNameAndImage => 'No Changes to Name and Image';

  @override
  String get failedToSaveUpdatedProfile => 'Failed to save updated profile';

  @override
  String get topics => 'Topics';

  @override
  String get emailIsRequired => 'Email is required';

  @override
  String get passwordIsRequired => 'Password is required';

  @override
  String get passwordMustBeAtLeast8CharactersLong =>
      'Password must be at least 8 characters long';

  @override
  String get passwordMustContainAtLeastOneUppercaseLetter =>
      'Password must contain at least one uppercase letter';

  @override
  String get passwordMustContainAtLeastOneLowercaseLetter =>
      'Password must contain at least one lowercase letter';

  @override
  String get passwordMustContainAtLeastOneDigit =>
      'Password must contain at least one digit';

  @override
  String get passwordMustContainAtLeastOneSpecialCharacter =>
      'Password must contain at least one special character';

  @override
  String get pleaseReEnterYourPassword => 'Please re-enter your password';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get nameIsRequired => 'Name is required';

  @override
  String get validEmail => 'Enter a valid email address';

  @override
  String get fromChannelsYouFollowed => 'From Channels You Followed';

  @override
  String get humidity => 'Humidity';

  @override
  String get wind => 'Wind';

  @override
  String get pressure => 'Pressure';

  @override
  String get editingComment => 'Editing Comment';

  @override
  String get edit => 'সম্পাদনা করুন';

  @override
  String get delete => 'মুছুন';

  @override
  String get report => 'রিপোর্ট করুন';

  @override
  String get reportComment => 'Report Comment';

  @override
  String get yourComment => 'Your Comment';

  @override
  String get cancel => 'Cancel';

  @override
  String get send => 'Send';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get speakLoud => 'Speak Loud';

  @override
  String get clickHereToReadMore => 'Click here to read more';

  @override
  String get save => 'সংরক্ষণ করুন';

  @override
  String get areYouSure => 'আপনি কি নিশ্চিত?';

  @override
  String get doYouWantToDeleteYourAccountWithThisAction =>
      'Do you want to delete your account with this action?';

  @override
  String get currentSize => 'Current Size';

  @override
  String get aLable => 'A';

  @override
  String get selectLanguages => 'Select Languages';

  @override
  String get retry => 'পুনরায় চেষ্টা করুন';

  @override
  String get noLanguagesAvailable => 'No languages available';

  @override
  String get continueWith => 'Continue with';

  @override
  String get languages => 'Languages';

  @override
  String get selectLanguagesToContinue => 'Select languages to continue';

  @override
  String get signInToAccessAllFeatures =>
      'সমস্ত বৈশিষ্ট্য ব্যবহার করতে সাইন ইন করুন';

  @override
  String get tapToEditProfile => 'প্রোফাইল সম্পাদনা করতে ট্যাপ করুন';

  @override
  String get myAccount => 'আমার অ্যাকাউন্ট';

  @override
  String get newsLanguages => 'সংবাদের ভাষা';

  @override
  String get transaction => 'লেনদেন';

  @override
  String get textSize => 'লিখার আকার';

  @override
  String get supportAndAbout => 'সহায়তা ও সম্পর্কে';

  @override
  String get legal => 'আইনি';

  @override
  String get month => 'মাস';

  @override
  String get adFreeExperience => 'বিজ্ঞাপন মুক্ত অভিজ্ঞতা';

  @override
  String get accessTo => 'অ্যাক্সেস';

  @override
  String get articles => 'প্রবন্ধ';

  @override
  String get active => 'সক্রিয়';

  @override
  String get selectDuration => 'সময়সীমা নির্বাচন করুন';

  @override
  String get membership => 'সদস্যতা';

  @override
  String get tapToViewMembershipOptions =>
      'সদস্যতার বিকল্পগুলি দেখতে ট্যাপ করুন';

  @override
  String get buyPlan => 'প্ল্যান কিনুন';

  @override
  String get inactive => 'নিষ্ক্রিয়';

  @override
  String get couldNotLoadSubscriptionDetails =>
      'সাবস্ক্রিপশন বিস্তারিত লোড করা যায়নি';

  @override
  String get na => 'প্রযোজ্য নয়';

  @override
  String get duration => 'সময়কাল';

  @override
  String get months => 'মাস';

  @override
  String get expiresOn => 'মেয়াদ শেষ হবে';

  @override
  String get adsFree => 'বিজ্ঞাপন মুক্ত';

  @override
  String get yes => 'হ্যাঁ';

  @override
  String get no => 'না';

  @override
  String get remainingDays => 'অবশিষ্ট দিন';

  @override
  String get youreachedPostLimitBuyPlan =>
      'আপনি আপনার বিনামূল্যের পোস্ট সীমা অতিক্রম করেছেন, প্ল্যান কিনুন';

  @override
  String get youreachedStoriesLimitBuyPlan =>
      'আপনি আপনার বিনামূল্যের গল্প সীমা অতিক্রম করেছেন, প্ল্যান কিনুন';

  @override
  String get limitReached => 'আপনি সর্বাধিক সীমায় পৌঁছে গেছেন';

  @override
  String get oKGotIt => 'ঠিক আছে, বুঝেছি';

  @override
  String get discoverChannels => 'চ্যানেল আবিষ্কার করুন';

  @override
  String get transactionHistory => 'লেনদেনের ইতিহাস';

  @override
  String get noTransactionsYet => 'এখনও কোনও লেনদেন নেই';

  @override
  String get onceYouStartMakingTransactions =>
      'আপনি যখন লেনদেন শুরু করবেন, তখন সেগুলি এখানে দেখা যাবে।';

  @override
  String get payment => 'পেমেন্ট';

  @override
  String get extraSmall => 'অতিকিঞ্চিৎ';

  @override
  String get small => 'ছোট';

  @override
  String get medium => 'মধ্যম';

  @override
  String get large => 'বড়';

  @override
  String get extraLarge => 'অতিবৃহৎ';

  @override
  String get selectFontSize => 'ফন্ট সাইজ নির্বাচন করুন';

  @override
  String get includedFeatures => 'অন্তর্ভুক্ত বৈশিষ্ট্যগুলি';

  @override
  String get noPlansAvailable => 'কোনও পরিকল্পনা উপলব্ধ নেই';

  @override
  String get noPlansDescription =>
      'আমরা এই মুহূর্তে কোনও সদস্যতার পরিকল্পনা খুঁজে পাইনি। অনুগ্রহ করে পরে আবার চেষ্টা করুন।';

  @override
  String get isCurrentlyEmpty => 'বর্তমানে খালি';

  @override
  String get noContentAvailable =>
      'মনে হচ্ছে বর্তমানে কোনো কনটেন্ট উপলব্ধ নেই। পরে আবার চেষ্টা করুন বা রিফ্রেশ করুন।';
}
