


import 'package:equatable/equatable.dart';

import '../../Model/device_fcm_model.dart';

abstract class Device<PERSON>MState extends Equatable {
  @override
  List<Object?> get props => [];
}

class DeviceFCMInitialState extends DeviceFCMState {}

class Device<PERSON>MLoadingState extends Device<PERSON>MState {}

class Device<PERSON><PERSON>uccessState extends Device<PERSON>MState {
  final List<DeviceFCMResponse> fcmData;

  DeviceFCMSuccessState({required this.fcmData});

  @override
  List<Object?> get props => [fcmData];
}

class DeviceFCMErrorState extends DeviceFCMState {
  final String errorMessage;

  DeviceFCMErrorState({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
